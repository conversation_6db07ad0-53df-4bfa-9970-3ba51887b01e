<!DOCTYPE html>
<html lang="en" style="margin:0;padding:0;">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
  <title>${EMAIL_TITLE}</title>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap" rel="stylesheet">
  <style>
    /* ===== BASE STYLES ===== */
    body { 
      margin:0; 
      padding:0; 
      background:#d9d9d9; 
      font-family:'Montserrat',-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",<PERSON><PERSON>,"Source Han Sans SC","Source Han Sans CN","Source Han Sans TC","Source Han Sans JP","Source Han Sans KR","PingFang SC","PingFang TC","PingFang HK","Hiragino Sans GB","Microsoft YaHei","Microsoft JhengHei","Noto Sans CJK SC","Noto Sans CJK TC","Noto Sans CJK JP","Noto Sans CJK KR",sans-serif; 
    }
    table, td { border-collapse:collapse; }
    
    /* ===== CONTAINER ===== */
    .email-container { 
      max-width:600px; 
      margin:0 auto; 
      background:#ffffff; 
      border-radius:8px; 
      box-shadow:0 0px 20px rgba(0,0,0,0.3); 
      overflow:hidden; 
    }
    
    /* ===== HEADER ===== */
    .header { 
      background:#000000; 
      padding:40px 20px; 
      text-align:center; 
      height:36px; 
      position:relative; 
      overflow:hidden; 
    }
    .header h1 { 
      margin:0; 
      color:#d8ff18; 
      font-size:32px; 
      font-weight:700; 
      letter-spacing:1px; 
      font-style:italic; 
      position:absolute; 
      top:50%; 
      left:50%; 
      transform:translate(-50%,-50%); 
      z-index:2; 
      text-shadow:2px 2px 4px rgba(0,0,0,0.5); 
    }
    
    /* ===== CONTENT AREA ===== */
    .content { 
      padding:44px 32px; 
      text-align:left; 
    }
    .content-center { text-align:center; }
    .content-left { text-align:left; }
    
    /* ===== EMOJI ICONS ===== */
    .emoji-icon { 
      font-size:64px; 
      margin:0 auto 16px; 
      display:block; 
      text-align:center; 
      line-height:1; 
    }
    .emoji-icon-small { 
      font-size:32px; 
      margin:0 auto 12px; 
      display:block; 
      text-align:center; 
      line-height:1; 
    }
    .emoji-icon-tiny { 
      font-size:16px; 
      margin:0 4px 0 0; 
      display:inline-block; 
      vertical-align:middle; 
    }
    
    /* ===== TYPOGRAPHY ===== */
    .title { 
      font-size:24px; 
      color:#1c1c1c; 
      margin:0 0 44px 0; 
      text-align:center; 
      font-weight:700; 
    }
    .title-with-icon { 
      font-size:24px; 
      color:#1c1c1c; 
      margin:0 0 44px 0; 
      text-align:center; 
      font-weight:700; 
    }
    .subtitle { 
      font-size:18px; 
      font-weight:600; 
      margin:0 0 16px 0; 
      color:#333; 
    }
    .greeting { 
      font-size:16px; 
      line-height:1.6; 
      color:#555555; 
      margin:0 0 16px 0; 
    }
    .desc { 
      font-size:16px; 
      line-height:1.6; 
      color:#555555; 
      margin:0 0 16px 0; 
    }
    .note { 
      font-size:12px; 
      color:#888888; 
      margin:16px 0 0 0; 
      line-height:1.3; 
    }
    .small-text { 
      font-size:14px; 
      color:#666666; 
      line-height:1.4; 
    }
    
    /* ===== CODE/VERIFICATION ===== */
    .code-box { 
      background:#f2f2f2; 
      border-radius:12px; 
      padding:20px; 
      text-align:center; 
      margin:30px 0; 
    }
    .code { 
      margin:0; 
      font-size:32px; 
      letter-spacing:5px; 
      color:#000000; 
      font-weight:700; 
    }
    .code-small { 
      margin:0; 
      font-size:24px; 
      letter-spacing:3px; 
      color:#000000; 
      font-weight:700; 
    }
    
    /* ===== BUTTONS ===== */
    .button { 
      display:block; 
      background:#c8ff29; 
      color:#1c1c1c; 
      padding:15px 35px; 
      border-radius:8px; 
      text-decoration:none; 
      font-size:18px; 
      font-weight:bold; 
      margin:0; 
      text-align:center; 
      width:100%; 
      box-sizing:border-box; 
    }
    .button:hover { background:#b3e61a; }
    .button-primary { background:#c8ff29; color:#1c1c1c; }
    .button-success { background:#4CAF50; color:#fff; }
    .button-danger { background:#f44336; color:#fff; }
    .button-warning { background:#ff9800; color:#fff; }
    .button-info { background:#2196F3; color:#fff; }
    .button-small { 
      padding:10px 20px; 
      font-size:14px; 
    }
    .button-large { 
      padding:18px 40px; 
      font-size:20px; 
    }
    
    /* ===== ALERT BOXES ===== */
    .alert { 
      padding:16px; 
      border-radius:6px; 
      margin:16px 0; 
      font-size:14px; 
    }
    .alert-success { 
      background:#d4edda; 
      color:#155724; 
      border:1px solid #c3e6cb; 
    }
    .alert-error { 
      background:#f8d7da; 
      color:#721c24; 
      border:1px solid #f5c6cb; 
    }
    .alert-warning { 
      background:#fff3cd; 
      color:#856404; 
      border:1px solid #ffeaa7; 
    }
    .alert-info { 
      background:#d1ecf1; 
      color:#0c5460; 
      border:1px solid #bee5eb; 
    }
    .alert-with-icon { 
      display:flex; 
      align-items:center; 
      gap:12px; 
    }
    .alert-icon { 
      font-size:20px; 
      flex-shrink:0; 
    }
    
    /* ===== HIGHLIGHT TEXT ===== */
    .highlight { color:#f60505; font-weight:600; }
    .highlight-success { color:#4CAF50; font-weight:600; }
    .highlight-primary { color:#1c1c1c; font-weight:600; }
    .highlight-warning { color:#ff9800; font-weight:600; }
    .highlight-info { color:#2196F3; font-weight:600; }
    
    /* ===== CARDS ===== */
    .card { 
      border:1px solid #e0e0e0; 
      border-radius:8px; 
      padding:16px 24px; 
      margin:16px 0; 
      text-align:left; 
    }
    .card-success { 
      border-left:4px solid #4CAF50; 
      background:#f8fff8; 
    }
    .card-error { 
      border-left:4px solid #f44336; 
      background:#fff8f8; 
    }
    .card-warning { 
      border-left:4px solid #ff9800; 
      background:#fffef8; 
    }
    .card-info { 
      border-left:4px solid #2196F3; 
      background:#f8fbff; 
    }
    .card-header { 
      font-weight:600; 
      margin-bottom:16px; 
      color:#333; 
    }
    .card-content { 
      color:#555; 
      line-height:1.5; 
    }
    
    /* ===== LISTS ===== */
    .list { 
      list-style-type:none; 
      padding-left:0; 
      text-align:left; 
      list-style:none; 
      padding:0; 
    }
    .list li { 
      margin-bottom:10px; 
      padding-left:16px; 
      position:relative; 
      height:20px; 
      line-height:20px; 
    }
    .list li::before { 
      content:'•'; 
      position:absolute; 
      left:0; 
      color:#c8ff29; 
      font-size:20px; 
      line-height:20px; 
    }
    .list .list-title { 
      font-weight:bold; 
      margin-bottom:15px; 
      padding-left:0; 
    }
    .list .list-title::before { 
      content:none; 
    }
    .list-success li::before { color:#4CAF50; }
    .list-error li::before { color:#f44336; }
    .list-warning li::before { color:#ff9800; }
    .list-info li::before { color:#2196F3; }
    
    /* ===== CLOUD TEMPLATE STANDARDS ===== */
    /*
    Cloud (C类) Template Design Standards:
    
    1. Content Structure:
       - Header (Get3D.AI)
       - Emoji Icon + Title (Centered)
       - Greeting (Hi userName)
       - Description (Core message)
       - CTA Button (Primary action)
       - Info Section (Task/Invite/Expiry details)
       - Alert OR Card (Secondary info/actions)
       - Note (Footer note)
       - Footer (Contact info)
    
    2. Visual Hierarchy:
       - task-info: Prominent, formal information display
       - card: Lightweight, friendly supplementary info
       - alert: Important warnings or critical info
       - card-list: Ordered list with numbered items
    
    3. Color Themes:
       - Success: Green (#4CAF50)
       - Warning: Orange (#FF9800)
       - Error: Red (#F44336)
       - Info: Blue (#2196F3)
    
    4. Spacing Standards:
       - Content padding: 44px 32px
       - Section margins: 24px 0
       - Card padding: 20px
       - List item spacing: 3px
    */
    
    /* ===== TASK INFO STYLES ===== */
    .task-info { 
      background:#ffffff; 
      border-radius:16px; 
      padding:28px; 
      margin:32px 0; 
      border:2px solid #e8f5e8; 
      box-shadow:0 4px 12px rgba(76, 175, 80, 0.1); 
      position:relative; 
    }
    .task-info::before { 
      content:''; 
      position:absolute; 
      left:0; 
      top:0; 
      bottom:0; 
      width:6px; 
      background:linear-gradient(180deg, #4CAF50 0%, #66BB6A 100%); 
      border-radius:16px 0 0 16px; 
    }
    .task-name { 
      font-size:20px; 
      font-weight:700; 
      color:#1c1c1c; 
      margin-bottom:16px; 
      padding-left:8px; 
    }
    .task-detail { 
      display:flex; 
      justify-content:space-between; 
      align-items:center; 
      margin-bottom:8px; 
      font-size:14px; 
      padding:12px 16px; 
      background:#00000008; 
      border-radius:8px; 
    }
    .task-label { 
      color:#555555; 
      font-weight:600; 
    }
    .task-value { 
      color:#1c1c1c; 
      font-weight:700; 
    }
    
    /* ===== CARD LIST STYLES ===== */
    .card-list { 
      margin:12px 0; 
    }
    .card-list-item { 
      display:flex; 
      align-items:flex-start; 
      background:rgba(255,255,255,0.6); 
      border-radius:8px; 
      padding:12px; 
      margin-bottom:3px; 
      border:1px solid rgba(76, 175, 80, 0.2); 
    }
    .card-list-number { 
      width:24px; 
      height:24px; 
      background:rgba(76, 175, 80, 0.8); 
      border-radius:50%; 
      display:flex; 
      align-items:center; 
      justify-content:center; 
      margin-right:12px; 
      flex-shrink:0; 
      font-weight:600; 
      font-size:12px; 
      color:#ffffff; 
    }
    .card-list-content { 
      flex:1; 
    }
    .card-list-title { 
      font-weight:600; 
      font-size:14px; 
      color:#2c3e50; 
      margin-bottom:2px; 
      line-height:1.3; 
    }
    .card-list-desc { 
      font-size:13px; 
      color:#666666; 
      line-height:1.4; 
    }
    
    /* ===== FAILURE REASON STYLES ===== */
    .failure-reason { 
      background:#fff5f5; 
      border-radius:12px; 
      padding:20px; 
      margin:24px 0; 
      border:1px solid #fed7d7; 
    }
    .failure-reason-title { 
      font-size:16px; 
      font-weight:700; 
      color:#c53030; 
      margin-bottom:12px; 
    }
    .failure-reason-text { 
      font-size:15px; 
      color:#2d3748; 
      line-height:1.5; 
      padding:12px; 
      background:#f7fafc; 
      border-radius:8px; 
      border-left:4px solid #e53e3e; 
    }
    
    /* ===== DELETION INFO STYLES ===== */
    .deletion-info { 
      background:#ffffff; 
      border-radius:16px; 
      padding:28px; 
      margin:32px 0; 
      border:2px solid #fed7d7; 
      box-shadow:0 4px 12px rgba(245, 101, 101, 0.1); 
      position:relative; 
    }
    .deletion-info::before { 
      content:''; 
      position:absolute; 
      left:0; 
      top:0; 
      bottom:0; 
      width:6px; 
      background:linear-gradient(180deg, #e53e3e 0%, #fc8181 100%); 
      border-radius:16px 0 0 16px; 
    }
    .deletion-title { 
      font-size:20px; 
      font-weight:700; 
      color:#1c1c1c; 
      margin-bottom:16px; 
      padding-left:8px; 
    }
    .deletion-detail { 
      display:flex; 
      justify-content:space-between; 
      align-items:center; 
      margin-bottom:12px; 
      font-size:15px; 
      padding:8px 12px; 
      background:#f8f9fa; 
      border-radius:8px; 
    }
    .deletion-label { 
      color:#555555; 
      font-weight:600; 
    }
    .deletion-value { 
      color:#1c1c1c; 
      font-weight:700; 
    }
    .deletion-notice { 
      margin-top:20px; 
      padding:16px; 
      background:#fff5f5; 
      border-radius:8px; 
      border:1px solid #fed7d7; 
    }
    .deletion-notice-title { 
      font-size:16px; 
      font-weight:700; 
      color:#c53030; 
      margin-bottom:8px; 
    }
    .deletion-notice-text { 
      font-size:14px; 
      color:#2d3748; 
      line-height:1.5; 
    }
    
    /* ===== EXPIRY INFO STYLES ===== */
    .expiry-info { 
      background:#ffffff; 
      border-radius:16px; 
      padding:28px; 
      margin:32px 0; 
      border:2px solid #fef5e7; 
      box-shadow:0 4px 12px rgba(255, 152, 0, 0.1); 
      position:relative; 
    }
    .expiry-info::before { 
      content:''; 
      position:absolute; 
      left:0; 
      top:0; 
      bottom:0; 
      width:6px; 
      background:linear-gradient(180deg, #ff9800 0%, #ffb74d 100%); 
      border-radius:16px 0 0 16px; 
    }
    .expiry-title { 
      font-size:20px; 
      font-weight:700; 
      color:#1c1c1c; 
      margin-bottom:16px; 
      padding-left:8px; 
    }
    .expiry-detail { 
      display:flex; 
      justify-content:space-between; 
      align-items:center; 
      margin-bottom:12px; 
      font-size:15px; 
      padding:8px 12px; 
      background:#f8f9fa; 
      border-radius:8px; 
    }
    .expiry-label { 
      color:#555555; 
      font-weight:600; 
    }
    .expiry-value { 
      color:#1c1c1c; 
      font-weight:700; 
    }
    .expiry-warning { 
      margin-top:20px; 
      padding:16px; 
      background:#fffbf0; 
      border-radius:8px; 
      border:1px solid #fef5e7; 
    }
    .expiry-warning-title { 
      font-size:16px; 
      font-weight:700; 
      color:#d69e2e; 
      margin-bottom:8px; 
    }
    .expiry-warning-text { 
      font-size:14px; 
      color:#2d3748; 
      line-height:1.5; 
    }
    
    /* ===== STATUS BADGES ===== */
    .badge { 
      display:inline-block; 
      padding:4px 8px; 
      border-radius:4px; 
      font-size:12px; 
      font-weight:600; 
      text-transform:uppercase; 
    }
    .badge-success { background:#4CAF50; color:#fff; }
    .badge-error { background:#f44336; color:#fff; }
    .badge-warning { background:#ff9800; color:#fff; }
    .badge-info { background:#2196F3; color:#fff; }
    .badge-primary { background:#c8ff29; color:#1c1c1c; }
    
    /* ===== DIVIDERS ===== */
    .divider { 
      height:1px; 
      background:#e0e0e0; 
      margin:24px 0; 
    }
    .divider-dashed { 
      height:1px; 
      background:repeating-linear-gradient(to right, #e0e0e0 0, #e0e0e0 4px, transparent 4px, transparent 8px); 
      margin:24px 0; 
    }
    
    /* ===== UTILITY CLASSES ===== */
    .text-center { text-align:center; }
    .text-left { text-align:left; }
    .text-right { text-align:right; }
    .mb-0 { margin-bottom:0; }
    .mb-8 { margin-bottom:8px; }
    .mb-16 { margin-bottom:16px; }
    .mb-24 { margin-bottom:24px; }
    .mb-32 { margin-bottom:32px; }
    .mt-0 { margin-top:0; }
    .mt-8 { margin-top:8px; }
    .mt-16 { margin-top:16px; }
    .mt-24 { margin-top:24px; }
    .mt-32 { margin-top:32px; }
    .p-0 { padding:0; }
    .p-16 { padding:16px; }
    .p-24 { padding:24px; }
    
    /* ===== FOOTER ===== */
    .footer { 
      background:#f2f2f2 !important; 
      background-color:#f2f2f2 !important; 
      padding:16px; 
      text-align:center; 
      font-size:12px; 
      color:#888888; 
    }
    .footer a { 
      color:#444444; 
      text-decoration:none; 
      margin:0 5px; 
    }
    .contact { 
      color:#2b2b2b; 
      font-size:16px; 
      margin:8px 0; 
    }
    
    /* ===== RESPONSIVE ===== */
    @media screen and (max-width:400px) {
      body { 
        padding:4px; 
        font-size:16px; 
      }
      .footer { 
        background:#f2f2f2 !important; 
      }
      .content { 
        padding:20px; 
      }
      .title { 
        font-size:20px; 
        margin-bottom:32px; 
      }
      .emoji-icon { 
        font-size:48px; 
      }
      .emoji-icon-small { 
        font-size:24px; 
      }
      .code { 
        font-size:24px; 
        letter-spacing:3px; 
      }
      .code-small { 
        font-size:18px; 
        letter-spacing:2px; 
      }
      .button { 
        padding:12px 24px; 
        font-size:16px; 
      }
      .button-large { 
        padding:14px 28px; 
        font-size:18px; 
      }
      .button-small { 
        padding:8px 16px; 
        font-size:12px; 
      }
      
      /* Task Info Responsive */
      .task-info { 
        padding:20px; 
        margin:20px 0; 
      }
      .task-name { 
        font-size:18px; 
        margin-bottom:12px; 
      }
      .task-detail { 
        font-size:14px; 
        padding:6px 8px; 
        margin-bottom:8px; 
      }
      
      /* Failure Reason Responsive */
      .failure-reason { 
        padding:16px; 
        margin:16px 0; 
      }
      .failure-reason-title { 
        font-size:14px; 
        margin-bottom:8px; 
      }
      .failure-reason-text { 
        font-size:13px; 
        padding:8px; 
      }
      
      /* Deletion Info Responsive */
      .deletion-info { 
        padding:20px; 
        margin:20px 0; 
      }
      .deletion-title { 
        font-size:18px; 
        margin-bottom:12px; 
      }
      .deletion-detail { 
        font-size:14px; 
        padding:6px 8px; 
        margin-bottom:8px; 
      }
      .deletion-notice { 
        padding:12px; 
        margin-top:16px; 
      }
      .deletion-notice-title { 
        font-size:14px; 
        margin-bottom:6px; 
      }
      .deletion-notice-text { 
        font-size:13px; 
      }
      
      /* Expiry Info Responsive */
      .expiry-info { 
        padding:20px; 
        margin:20px 0; 
      }
      .expiry-title { 
        font-size:18px; 
        margin-bottom:12px; 
      }
      .expiry-detail { 
        font-size:14px; 
        padding:6px 8px; 
        margin-bottom:8px; 
      }
      .expiry-warning { 
        padding:12px; 
        margin-top:16px; 
      }
      .expiry-warning-title { 
        font-size:14px; 
        margin-bottom:6px; 
      }
      .expiry-warning-text { 
        font-size:13px; 
      }
      
      /* Card List Responsive */
      .card-list-item { 
        padding:10px; 
        margin-bottom:2px; 
      }
      .card-list-number { 
        width:20px; 
        height:20px; 
        font-size:10px; 
        margin-right:10px; 
      }
      .card-list-title { 
        font-size:13px; 
        margin-bottom:1px; 
      }
      .card-list-desc { 
        font-size:12px; 
      }
      
      /* Alert Responsive */
      .alert { 
        padding:12px; 
        margin:12px 0; 
        font-size:13px; 
      }
      .alert-icon { 
        font-size:16px; 
      }
      
      /* Card Responsive */
      .card { 
        padding:12px 16px; 
        margin:12px 0; 
      }
      .card-header { 
        font-size:15px; 
        margin-bottom:6px; 
      }
      .card-content { 
        font-size:13px; 
      }
      
      /* Badge Responsive */
      .badge { 
        padding:3px 6px; 
        font-size:10px; 
      }
      
      /* Utility Classes Responsive */
      .mb-32 { margin-bottom:20px; }
      .mt-32 { margin-top:20px; }
      .p-24 { padding:16px; }
    }
    
    /* ===== PRODUCT THEME COLORS ===== */
    .product-mapper { 
      background:linear-gradient(135deg, #B8FF00 0%, #A3E600 100%); 
      color:#1c1c1c; 
    }
    .product-modelfun { 
      background:linear-gradient(135deg, #18C683 0%, #15A06A 100%); 
      color:#ffffff; 
    }
    .product-viewer { 
      background:linear-gradient(135deg, #FF0EFF 0%, #E600E6 100%); 
      color:#ffffff; 
    }
    .product-cloud { 
      background:linear-gradient(135deg, #22A7F0 0%, #1E96D9 100%); 
      color:#ffffff; 
    }
    .product-package { 
      background:linear-gradient(130deg, #B7FE01 5.61%, #18C683 29.08%, #FF0EFF 89.95%); 
      color:#ffffff; 
    }
    
    /* ===== EMAIL CLIENT COMPATIBILITY ===== */
    table, td { mso-table-lspace:0pt; mso-table-rspace:0pt; }
    img { -ms-interpolation-mode:bicubic; }
    a[x-apple-data-detectors] { color:inherit !important; text-decoration:none !important; }
    
    /* ===== CRITICAL TEMPLATE RULES ===== */
    /* 
    ⚠️  CRITICAL TEMPLATE DEVELOPMENT RULES  ⚠️
    
    1. VERIFICATION CODE TEMPLATES (A01, A02, A04):
       - MUST use alert-warning, NEVER alert-info
       - ALL verification codes expire in 5 minutes, NOT 10 minutes
       - MUST follow identical structure:
         * Greeting
         * Description of what the code is for
         * Code box with ${code}
         * Instructions to copy code to form
         * Warning alert with 5-minute expiry
         * Note about ignoring if not requested
    
    2. TEMPLATE CONSISTENCY:
       - Templates of the same type must be highly similar in structure and wording
       - Use consistent variable names: ${code}, ${userName}, ${mail}
       - Always include required CSS styles in each template
    
    3. CSS STYLES:
       - Each template must include ALL necessary CSS styles
       - Don't rely on Base Template styles being available
       - Include alert-warning, code-box, and code styles in verification templates
    
    4. CONTENT STRUCTURE:
       - Keep descriptions concise and clear
       - Use consistent terminology across similar templates
       - Maintain professional but friendly tone
    
    5. VARIABLE USAGE:
       - ${code} for verification codes
       - ${userName} for user names
       - ${mail} for email addresses
       - ${cta_link} for call-to-action links
       
    6. CTA LINK RULES (FIXED URLs):
       - Go to My Account: https://www.get3d.ai/personal-center/subscription
       - Read the Guide: https://docs.get3d.ai/
       - Watch the Video: https://www.youtube.com/@Get3D_Official
       - Download: https://www.get3d.ai/others/downloads
       - Cloud: https://www.get3d.ai/cloud/#/velora
       - NEVER use variables for these links, use fixed URLs
    */
  </style>
</head>
<body>
  <div class="email-container">
    <!-- Header -->
    <div class="header">
      <h1>Get3D</h1>
    </div>
    
    <!-- Content -->
    <div class="content">
      <!-- ===== 常用组件示例 ===== -->
      
      <!-- 1. 图标 + 标题组合 -->
      <div class="content-center">
        <div class="emoji-icon">🔐</div>
        <h2 class="title-with-icon">Email Verification</h2>
      </div>
      
      <!-- 2. 欢迎语 -->
      <p class="greeting">Hi ${userName},</p>
      
      <!-- 3. 正文内容 -->
      <p class="desc">This is a sample description text that demonstrates the standard paragraph styling.</p>
      
      <!-- 4. 验证码框 -->
      <div class="code-box">
        <div class="code">${verificationCode}</div>
      </div>
      
      <!-- 5. 主要按钮 -->
      <div class="text-center">
        <a href="${cta_link}" class="button button-primary">Verify Email</a>
      </div>
      
      <!-- 6. 成功提示框 -->
      <div class="alert alert-success">
        <div class="alert-with-icon">
          <span class="alert-icon">✅</span>
          <span>Your account has been successfully verified!</span>
        </div>
      </div>
      
      <!-- 7. 警告提示框 -->
      <div class="alert alert-warning">
        <div class="alert-with-icon">
          <span class="alert-icon">⚠️</span>
          <span>This verification code will expire in 10 minutes.</span>
        </div>
      </div>
      
      <!-- 8. 信息提示框 -->
      <div class="alert alert-info">
        <div class="alert-with-icon">
          <span class="alert-icon">ℹ️</span>
          <span>Need help? Contact our support team.</span>
        </div>
      </div>
      
      <!-- 9. 错误提示框 -->
      <div class="alert alert-error">
        <div class="alert-with-icon">
          <span class="alert-icon">❌</span>
          <span>Verification failed. Please try again.</span>
        </div>
      </div>
      
      <!-- 10. 成功卡片 -->
      <div class="card card-success">
        <div class="card-header">🎉 Registration Successful</div>
        <div class="card-content">Welcome to Get3D! Your account is now active and ready to use.</div>
      </div>
      
      <!-- 11. 警告卡片 -->
      <div class="card card-warning">
        <div class="card-header">⏰ Trial Expiring Soon</div>
        <div class="card-content">Your free trial will expire on ${expiryDate}. Upgrade now to continue using our services.</div>
      </div>
      
      <!-- 12. 信息卡片 -->
      <div class="card card-info">
        <div class="card-header">📧 Team Invitation</div>
        <div class="card-content">${inviter} has invited you to join the ${teamName} team.</div>
      </div>
      
      <!-- 13. 错误卡片 -->
      <div class="card card-error">
        <div class="card-header">❌ Payment Failed</div>
        <div class="card-content">We couldn't process your payment. Please check your payment method and try again.</div>
      </div>
      
      <!-- 14. 状态徽章 -->
      <div class="text-center">
        <span class="badge badge-success">Active</span>
        <span class="badge badge-warning">Pending</span>
        <span class="badge badge-error">Expired</span>
        <span class="badge badge-info">Processing</span>
      </div>
      
      <!-- 15. 列表 -->
      <div class="card">
        <ul class="list">
          <li class="list-title">What you can do now:</li>
          <li>Access all Get3D products</li>
          <li>Create 3D models and maps</li>
          <li>Collaborate with team members</li>
        </ul>
      </div>
      
      <!-- 16. 成功列表 -->
      <div class="card">
        <ul class="list list-success">
          <li class="list-title">✅ Completed Tasks:</li>
          <li>Email verification</li>
          <li>Account setup</li>
          <li>Profile configuration</li>
        </ul>
      </div>
      
      <!-- 17. 分割线 -->
      <div class="divider"></div>
      
      <!-- 18. Cloud组件示例 -->
      
      <!-- 18.1 任务信息组件 (Task Info) -->
      <div class="task-info">
        <div class="task-name">${taskName}</div>
        <div class="task-detail">
          <span class="task-label">Start Time:</span>
          <span class="task-value">${startTime}</span>
        </div>
        <div class="task-detail">
          <span class="task-label">Completion Time:</span>
          <span class="task-value">${completionTime}</span>
        </div>
        <div class="task-detail">
          <span class="task-label">Total Duration:</span>
          <span class="task-value">${totalDuration}</span>
        </div>
        <div class="task-detail">
          <span class="task-label">Status:</span>
          <span class="task-value">✅ Completed Successfully</span>
        </div>
        <div class="task-detail">
          <span class="task-label">Photo Count:</span>
          <span class="task-value">${photoCount} photos</span>
        </div>
        <div class="task-detail">
          <span class="task-label">File Size:</span>
          <span class="task-value">${fileSize} GB</span>
        </div>
        <div class="task-detail">
          <span class="task-label">Modeling Area:</span>
          <span class="task-value">${modelingArea}</span>
        </div>
        <div class="task-detail">
          <span class="task-label">Model Quality:</span>
          <span class="task-value">${modelQuality}</span>
        </div>
      </div>
      
      <!-- 18.2 失败原因组件 (Failure Reason) -->
      <div class="failure-reason">
        <div class="failure-reason-title">Failure Reason:</div>
        <div class="failure-reason-text">${failureReason}</div>
      </div>
      
      <!-- 18.3 删除信息组件 (Deletion Info) -->
      <div class="deletion-info">
        <div class="deletion-title">Data Deletion Confirmation</div>
        <div class="deletion-detail">
          <span class="deletion-label">Deletion Date:</span>
          <span class="deletion-value">${deletionDate}</span>
        </div>
        <div class="deletion-detail">
          <span class="deletion-label">Grace Period:</span>
          <span class="deletion-value">${gracePeriodDays} days</span>
        </div>
        <div class="deletion-detail">
          <span class="deletion-label">Status:</span>
          <span class="deletion-value">❌ Permanently Deleted</span>
        </div>
        <div class="deletion-notice">
          <div class="deletion-notice-title">📋 What was deleted:</div>
          <div class="deletion-notice-text">
            All your 3D models, project files, and associated data have been permanently removed from our servers.
          </div>
        </div>
      </div>
      
      <!-- 18.4 过期信息组件 (Expiry Info) -->
      <div class="expiry-info">
        <div class="expiry-title">Cloud Trial Data Expiry Notice</div>
        <div class="expiry-detail">
          <span class="expiry-label">Expiry Date:</span>
          <span class="expiry-value">${endDate}</span>
        </div>
        <div class="expiry-detail">
          <span class="expiry-label">Status:</span>
          <span class="expiry-value">⚠️ Expiring Soon</span>
        </div>
        <div class="expiry-warning">
          <div class="expiry-warning-title">⚠️ Important:</div>
          <div class="expiry-warning-text">
            Your trial data will be permanently deleted after the grace period. Upgrade to a paid plan to keep your data safe.
          </div>
        </div>
      </div>
      
      <!-- 18.5 卡片列表组件 (Card List) -->
      <div class="card card-info">
        <div class="card-header">🚀 What you can do:</div>
        <div class="card-list">
          <div class="card-list-item">
            <div class="card-list-number">1</div>
            <div class="card-list-content">
              <div class="card-list-title">Download Files</div>
              <div class="card-list-desc">Export your 3D model and associated files to your local storage</div>
            </div>
          </div>
          <div class="card-list-item">
            <div class="card-list-number">2</div>
            <div class="card-list-content">
              <div class="card-list-title">Share with Team</div>
              <div class="card-list-desc">Invite team members to view and collaborate on your project</div>
            </div>
          </div>
          <div class="card-list-item">
            <div class="card-list-number">3</div>
            <div class="card-list-content">
              <div class="card-list-title">Continue Processing</div>
              <div class="card-list-desc">Start new modeling tasks or refine existing results</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 18.6 警告卡片列表 (Warning Card List) -->
      <div class="card card-warning">
        <div class="card-header">⚠️ Your options:</div>
        <div class="card-list">
          <div class="card-list-item">
            <div class="card-list-number">1</div>
            <div class="card-list-content">
              <div class="card-list-title">Start New Trial</div>
              <div class="card-list-desc">Begin a fresh free trial with new data and projects</div>
            </div>
          </div>
          <div class="card-list-item">
            <div class="card-list-number">2</div>
            <div class="card-list-content">
              <div class="card-list-title">Upgrade to Paid Plan</div>
              <div class="card-list-desc">Get permanent storage and keep your data safe</div>
            </div>
          </div>
          <div class="card-list-item">
            <div class="card-list-number">3</div>
            <div class="card-list-content">
              <div class="card-list-title">Contact Support</div>
              <div class="card-list-desc">Get help from our support team for any questions</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 19. 小文本 -->
      <p class="small-text">This is smaller text for additional information or disclaimers.</p>
      
      <!-- 19. 注释 -->
      <p class="note">If you didn't request this verification, please ignore this email.</p>
      
      <!-- 20. 高亮文本 -->
      <p class="desc">Your <span class="highlight-primary">${productName}</span> license has been <span class="highlight-success">activated</span> successfully!</p>
      
      <!-- 21. 小按钮 -->
      <div class="text-center">
        <a href="${secondary_link}" class="button button-small">Learn More</a>
      </div>
      
      <!-- 22. 大按钮 -->
      <div class="text-center">
        <a href="${primary_link}" class="button button-large">Get Started Now</a>
      </div>
      
      <!-- 23. 带图标的内联文本 -->
      <p class="desc">
        <span class="emoji-icon-tiny">🚀</span>
        New features available in ${version}
      </p>
      
      <!-- 24. 时间信息 -->
      <p class="desc">Task completed at <span class="highlight">${completionTime}</span></p>
      
      <!-- 25. 产品信息 -->
      <p class="desc">You're using <span class="highlight-primary">${productName}</span> version <span class="highlight-info">${version}</span></p>
      
      <!-- 26. 团队信息 -->
      <p class="desc">${userName} invited you to join <span class="highlight-primary">${teamName}</span></p>
      
      <!-- 27. 项目信息 -->
      <p class="desc">You've been added to project <span class="highlight-primary">${projectName}</span></p>
      
      <!-- 28. 任务信息 -->
      <p class="desc">Task <span class="highlight-primary">${taskName}</span> has been completed successfully!</p>
      
      <!-- 29. 渠道商信息 -->
      <p class="desc">You received a <span class="highlight-primary">${productName}</span> license from dealer <span class="highlight-info">${dealerName}</span></p>
      
      <!-- 30. 联系信息 -->
      <p class="desc">${fullName} (${email}) from ${company} is asking about ${topic}</p>
      
      <!-- 31. 优惠券信息 -->
      <p class="desc">Your exclusive coupon code: <span class="highlight-success">${couponCode}</span></p>
      
      <!-- 32. 订阅信息 -->
      <p class="desc">Your <span class="highlight-primary">${productName}</span> subscription expires on <span class="highlight-warning">${expiryDate}</span></p>
      
      <!-- 33. 试用信息 -->
      <p class="desc">Your <span class="highlight-primary">${productName}</span> free trial is ready!</p>
      
      <!-- 34. 版本更新信息 -->
      <p class="desc">Get3D <span class="highlight-primary">${productName}</span> <span class="highlight-info">${version}</span> is here!</p>
      
      <!-- 35. 支付信息 -->
      <p class="desc">Payment of <span class="highlight-success">${amount}</span> was successful!</p>
      
      <!-- 36. 数据管理信息 -->
      <p class="desc">Your cloud trial data expires on <span class="highlight-warning">${expiryDate}</span></p>
      
      <!-- 37. 组织邀请信息 -->
      <p class="desc">${userName} invited you to team <span class="highlight-primary">${teamName}</span></p>
      
      <!-- 38. 项目邀请信息 -->
      <p class="desc">${userName} invited you to project <span class="highlight-primary">${projectName}</span></p>
      
      <!-- 39. 渠道商邀请信息 -->
      <p class="desc">You are invited to become a Get3D partner</p>
      
      <!-- 40. 渠道商成功信息 -->
      <p class="desc">Congratulations! You're now a Get3D partner</p>
      
      <!-- 41. 用户唤醒信息 -->
      <p class="desc">We miss you! Come back and create magic</p>
      
      <!-- 42. 续费成功信息 -->
      <p class="desc">Your <span class="highlight-primary">${productName}</span> subscription renewed successfully</p>
      
      <!-- 43. 订阅取消信息 -->
      <p class="desc">Your <span class="highlight-primary">${productName}</span> subscription has been canceled</p>
      
      <!-- 44. 订阅暂停信息 -->
      <p class="desc">Your <span class="highlight-primary">${productName}</span> subscription has been suspended</p>
      
      <!-- 45. 应用审核信息 -->
      <p class="desc">Your application has been reviewed</p>
      
      <!-- 46. 作品成功信息 -->
      <p class="desc">Congratulations on Being Shortlisted!</p>
      
      <!-- 47. 作品失败信息 -->
      <p class="desc">Notification of Shortlisting Failure</p>
      
      <!-- 48. 发布成功信息 -->
      <p class="desc">Your 3D model is published successfully</p>
      
      <!-- 49. 发布失败信息 -->
      <p class="desc">Your 3D model publication failed</p>
      
      <!-- 50. 通用成功信息 -->
      <p class="desc">Operation completed successfully!</p>
      
      <!-- 51. 通用失败信息 -->
      <p class="desc">Operation failed. Please try again.</p>
      
      <!-- 52. 通用警告信息 -->
      <p class="desc">Please take action before the deadline.</p>
      
      <!-- 53. 通用信息提示 -->
      <p class="desc">Here's some important information for you.</p>
      
      <!-- 54. 通用过期信息 -->
      <p class="desc">This item has expired and is no longer available.</p>
      
      <!-- 55. 通用即将过期信息 -->
      <p class="desc">This item will expire soon. Please take action.</p>
      
      <!-- 56. 通用邀请信息 -->
      <p class="desc">You have received an invitation to join.</p>
      
      <!-- 57. 通用欢迎信息 -->
      <p class="desc">Welcome to Get3D! We're excited to have you on board.</p>
      
      <!-- 58. 通用感谢信息 -->
      <p class="desc">Thank you for choosing Get3D!</p>
      
      <!-- 59. 通用确认信息 -->
      <p class="desc">Your request has been confirmed.</p>
      
      <!-- 60. 通用处理中信息 -->
      <p class="desc">Your request is being processed. Please wait.</p>
      
      ${CONTENT_AREA}
    </div>
    
    <!-- Footer -->
    <div class="footer">
      <div class="contact"><EMAIL></div>
      <p>
        <a href="https://www.linkedin.com/company/get3d-ai/">LinkedIn</a> |
        <a href="https://www.youtube.com/@Get3D_Official">YouTube</a>
      </p>
      <p>&copy; 2025 Get3D, Inc. | #02-02, Reliance Building, 351 Jalan Besar, Singapore 208988</p>
    </div>
  </div>
</body>
</html> 