<!DOCTYPE html>
<html lang="en" style="margin:0;padding:0;">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
  <title>🎉 Your Get3D journey begins now</title>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap" rel="stylesheet">
  <style>
    /* ===== BASE STYLES ===== */
    body { 
      margin:0; 
      padding:0; 
      background:#d9d9d9; 
      font-family:'Montserrat',-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",<PERSON><PERSON>,"Source Han Sans SC","Source Han Sans CN","Source Han Sans TC","Source Han Sans JP","Source Han Sans KR","PingFang SC","PingFang TC","PingFang HK","Hiragino Sans GB","Microsoft YaHei","Microsoft JhengHei","Noto Sans CJK SC","Noto Sans CJK TC","Noto Sans CJK JP","Noto Sans CJK KR",sans-serif; 
    }
    table, td { border-collapse:collapse; }
    
    /* ===== CONTAINER ===== */
    .email-container { 
      max-width:600px; 
      margin:0 auto; 
      background:#ffffff; 
      border-radius:8px; 
      box-shadow:0 0px 20px rgba(0,0,0,0.3); 
      overflow:hidden; 
    }
    
    /* ===== HEADER ===== */
    .header { 
      background:#000000; 
      padding:40px 20px; 
      text-align:center; 
      height:36px; 
      position:relative; 
      overflow:hidden; 
    }
    .header h1 { 
      margin:0; 
      color:#d8ff18; 
      font-size:32px; 
      font-weight:700; 
      letter-spacing:1px; 
      font-style:italic; 
      position:absolute; 
      top:50%; 
      left:50%; 
      transform:translate(-50%,-50%); 
      z-index:2; 
      text-shadow:2px 2px 4px rgba(0,0,0,0.5); 
    }
    
    /* ===== CONTENT AREA ===== */
    .content { 
      padding:44px 32px; 
      text-align:left; 
    }
    .content-center { text-align:center; }
    .content-left { text-align:left; }
    
    /* ===== EMOJI ICONS ===== */
    .emoji-icon { 
      font-size:64px; 
      margin:0 auto 16px; 
      display:block; 
      text-align:center; 
      line-height:1; 
    }
    
    /* ===== TYPOGRAPHY ===== */
    .title { 
      font-size:24px; 
      color:#1c1c1c; 
      margin:0 0 44px 0; 
      text-align:center; 
      font-weight:700; 
    }
    .greeting { 
      font-size:16px; 
      line-height:1.6; 
      color:#555555; 
      margin:0 0 16px 0; 
    }
    .desc { 
      font-size:16px; 
      line-height:1.6; 
      color:#555555; 
      margin:0 0 16px 0; 
    }
    .note { 
      font-size:12px; 
      color:#888888; 
      margin:16px 0 0 0; 
      line-height:1.3; 
    }
    
    /* ===== BUTTONS ===== */
    .button { 
      display:block; 
      background:#c8ff29; 
      color:#1c1c1c; 
      padding:15px 35px; 
      border-radius:8px; 
      text-decoration:none; 
      font-size:18px; 
      font-weight:bold; 
      margin:0; 
      text-align:center; 
      width:100%; 
      box-sizing:border-box; 
    }
    .button:hover { background:#b3e61a; }
    .button-secondary { 
      background:#ffffff; 
      color:#333333; 
      border:1px solid #dddddd; 
    }
    .button-secondary:hover { 
      background:#f8f8f8; 
    }
    
    /* ===== ALERT BOXES ===== */
    .alert { 
      padding:16px; 
      border-radius:6px; 
      margin:16px 0; 
      font-size:14px; 
    }
    .alert-success { 
      background:#d4edda; 
      color:#155724; 
      border:1px solid #c3e6cb; 
    }
    .alert-with-icon { 
      display:flex; 
      align-items:center; 
      gap:12px; 
    }
    .alert-icon { 
      font-size:20px; 
      flex-shrink:0; 
    }
    
    /* ===== CARDS ===== */
    .card { 
      border:1px solid #e0e0e0; 
      border-radius:8px; 
      padding:16px 24px; 
      margin:16px 0; 
      text-align:left; 
    }
    .card-success { 
      border-left:4px solid #4CAF50; 
      background:#f8fff8; 
    }
    .card-header { 
      font-weight:600; 
      margin-bottom:16px; 
      color:#333; 
    }
    .card-content { 
      color:#555; 
      line-height:1.5; 
    }
    
    /* ===== BUTTON CONTAINER ===== */
    .button-container { 
      margin:32px 0; 
    }
    .secondary-buttons { 
      display:flex; 
      gap:16px; 
      margin-top:16px; 
    }
    .secondary-buttons .button { 
      flex:1; 
    }
    
    /* ===== PRODUCT CONTAINER ===== */
    .product-container { 
      margin:32px 0; 
    }
    .product-grid { 
      display:grid; 
      grid-template-columns:1fr 1fr; 
      gap:16px; 
      text-align:center; 
    }
    .product-card { 
      background:#ffffff; 
      border-radius:16px; 
      padding:20px 24px; 
      box-shadow:0 4px 16px rgba(0,0,0,0.08); 
      transition:all 0.3s ease; 
      position:relative; 
      overflow:hidden; 
      border-left:4px solid var(--theme-color); 
      text-align:left; 
    }
    .product-card:hover { 
      transform:translateY(-4px); 
      box-shadow:0 12px 32px rgba(0,0,0,0.15); 
    }
    .product-name { 
      font-size:18px; 
      font-weight:700; 
      color:#1c1c1c; 
      margin:0 0 12px 0; 
      line-height:1.3; 
    }
    .product-desc { 
      font-size:14px; 
      line-height:1.5; 
      color:#666666; 
      margin-bottom:16px; 
    }
    .card-footer { 
      display:flex; 
      justify-content:flex-start; 
    }
    .feature-tag { 
      background:var(--theme-light); 
      color:var(--feature-text-color, var(--theme-color)); 
      padding:4px 10px; 
      border-radius:8px; 
      font-size:10px; 
      font-weight:600; 
      text-transform:uppercase; 
      letter-spacing:0.2px; 
    }
    
    /* ===== ACTION BUTTONS ===== */
    .action-button { 
      position:absolute; 
      bottom:16px; 
      right:16px; 
      width:32px; 
      height:32px; 
      border-radius:8px; 
      display:flex; 
      align-items:center; 
      justify-content:center; 
      font-size:16px; 
      cursor:pointer; 
      transition:all 0.2s ease; 
      box-shadow:0 2px 8px rgba(0,0,0,0.1); 
    }
    .action-button:hover { 
      transform:scale(1.1); 
      box-shadow:0 4px 12px rgba(0,0,0,0.15); 
    }
    .download-btn { 
      background:var(--theme-gradient); 
      color:var(--badge-text-color); 
    }
    .cloud-btn { 
      background:var(--theme-gradient); 
      color:var(--badge-text-color); 
    }
    
    /* ===== PRODUCT THEME CARDS ===== */
    .mapper-card { 
      --theme-gradient:linear-gradient(135deg, #B8FF00 0%, #A3E600 100%); 
      --theme-color:#B8FF00; 
      --theme-light:rgba(184, 255, 0, 0.1); 
      --badge-text-color:#1c1c1c; 
      --feature-text-color:#2d5a00; 
    }
    .modelfun-card { 
      --theme-gradient:linear-gradient(135deg, #18C683 0%, #15A06A 100%); 
      --theme-color:#18C683; 
      --theme-light:rgba(24, 198, 131, 0.1); 
      --badge-text-color:#ffffff; 
      --feature-text-color:#0d5a3a; 
    }
    .viewer-card { 
      --theme-gradient:linear-gradient(135deg, #FF0EFF 0%, #E600E6 100%); 
      --theme-color:#FF0EFF; 
      --theme-light:rgba(255, 14, 255, 0.1); 
      --badge-text-color:#ffffff; 
      --feature-text-color:#b300b3; 
    }
    .cloud-card { 
      --theme-gradient:linear-gradient(135deg, #22A7F0 0%, #1E96D9 100%); 
      --theme-color:#22A7F0; 
      --theme-light:rgba(34, 167, 240, 0.1); 
      --badge-text-color:#ffffff; 
      --feature-text-color:#1a7bb8; 
    }
    
    /* ===== UTILITY CLASSES ===== */
    .text-center { text-align:center; }
    .mb-16 { margin-bottom:16px; }
    .mb-24 { margin-bottom:24px; }
    
    /* ===== FOOTER ===== */
    .footer { 
      background:#f2f2f2 !important; 
      background-color:#f2f2f2 !important; 
      padding:16px; 
      text-align:center; 
      font-size:12px; 
      color:#888888; 
    }
    .footer a { 
      color:#444444; 
      text-decoration:none; 
      margin:0 5px; 
    }
    .contact { 
      color:#2b2b2b; 
      font-size:16px; 
      margin:8px 0; 
    }
    
    /* ===== RESPONSIVE ===== */
    @media screen and (max-width:400px) {
      body { 
        padding:4px; 
        font-size:16px; 
      }
      .footer { 
        background:#f2f2f2 !important; 
      }
      .content { 
        padding:20px; 
      }
      .title { 
        font-size:20px; 
        margin-bottom:32px; 
      }
      .emoji-icon { 
        font-size:48px; 
      }
      .button { 
        padding:12px 24px; 
        font-size:16px; 
        width:100%; 
      }
      .product-grid { 
        grid-template-columns:1fr; 
        gap:12px; 
      }
      .product-card { 
        padding:16px 20px; 
      }
      .product-name { 
        font-size:16px; 
        margin-bottom:10px; 
      }
      .product-desc { 
        font-size:13px; 
        margin-bottom:12px; 
      }
      .feature-tag { 
        font-size:9px; 
        padding:3px 8px; 
      }
      .action-button { 
        width:28px; 
        height:28px; 
        bottom:12px; 
        right:12px; 
        font-size:14px; 
      }
      .secondary-buttons { 
        flex-direction:column; 
        gap:12px; 
      }
    }
    
    /* ===== EMAIL CLIENT COMPATIBILITY ===== */
    table, td { mso-table-lspace:0pt; mso-table-rspace:0pt; }
    img { -ms-interpolation-mode:bicubic; }
    a[x-apple-data-detectors] { color:inherit !important; text-decoration:none !important; }
  </style>
</head>
<body>
  <div class="email-container">
    <!-- Header -->
    <div class="header">
      <h1>Get3D</h1>
    </div>
    
    <!-- Content -->
    <div class="content">
      <div class="content-center">
        <div class="emoji-icon">🎉</div>
        <h2 class="title">Your Get3D journey begins now</h2>
      </div>
      
      <p class="greeting">Hi ${userName},</p>
      
      <p class="desc">Your Get3D.AI trial is active and ready to go, includes full access to all products until ${trialEndDate}.</p>
      
      <p class="desc">If you have any questions, our support team is always here to help.</p>

      <div class="button-container">
        <a href="https://www.get3d.ai/personal-center/subscription" class="button">Go to My Account</a>
        <div class="secondary-buttons">
          <a href="https://docs.get3d.ai/" class="button button-secondary">Read the Guide</a>
          <a href="https://www.youtube.com/@Get3D_Official" class="button button-secondary">Watch the Video</a>
        </div>
      </div>

      <div class="product-container">
        <div class="product-grid">
          <div class="product-card mapper-card">
            <div class="product-name">Mapper</div>
            <div class="product-desc">Automatically create 3D models on your PC</div>
            <div class="card-footer">
              <div class="feature-tag">Auto Generation</div>
            </div>
            <a href="https://www.get3d.ai/others/downloads" class="action-button download-btn">⬇️</a>
          </div>
          <div class="product-card modelfun-card">
            <div class="product-name">ModelFun</div>
            <div class="product-desc">Intuitive 3D model editing</div>
            <div class="card-footer">
              <div class="feature-tag">Easy to Use</div>
            </div>
            <a href="https://www.get3d.ai/others/downloads" class="action-button download-btn">⬇️</a>
          </div>
          <div class="product-card viewer-card">
            <div class="product-name">Viewer</div>
            <div class="product-desc">Professional 3D model viewing</div>
            <div class="card-footer">
              <div class="feature-tag">High Performance</div>
            </div>
            <a href="https://www.get3d.ai/others/downloads" class="action-button download-btn">⬇️</a>
          </div>
          <!-- <div class="product-card cloud-card">
            <div class="product-name">Cloud</div>
            <div class="product-desc">Advanced cloud-based 3D processing</div>
            <div class="card-footer">
              <div class="feature-tag">Cloud Powered</div>
            </div>
            <a href="https://www.get3d.ai/cloud/#/velora" class="action-button cloud-btn">☁️</a>
          </div> -->
        </div>
      </div>
      
      <p class="note">This email confirms your successful registration. You can now log in to your account and start exploring Get3D's features.</p>
    </div>
    
    <!-- Footer -->
    <div class="footer">
      <div class="contact"><EMAIL></div>
      <p>
        <a href="https://www.linkedin.com/company/get3d-ai/">LinkedIn</a> |
        <a href="https://www.youtube.com/@Get3D_Official">YouTube</a>
      </p>
      <p>&copy; 2025 Get3D, Inc. | #02-02, Reliance Building, 351 Jalan Besar, Singapore 208988</p>
    </div>
  </div>
</body>
</html> 