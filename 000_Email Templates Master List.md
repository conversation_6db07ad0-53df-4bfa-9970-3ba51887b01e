# Email Template Requirements / 邮件模板需求文档

[TOC]

## 🚀 v2版本优化总结 (2025-01-XX)

### 📋 主要优化内容

#### **🎯 设计原则升级**
- **"腾讯SES优化"策略**：针对腾讯SES服务的专门优化设计
- **"组件间距优化"理念**：通过外层table的垂直padding实现组件间距
- **"渐进增强"原则**：支持圆角和style内联样式，但避免阴影和渐变
- **"表格布局为王"**：使用 `<table>` 替代 `<div>` 布局
- **"堡垒级"防御策略**：针对Web内容安全网关的严格过滤机制

#### **🏗️ 技术架构改进**
- **文档类型**：升级为XHTML 1.0 Transitional
- **变量格式**：统一使用双括号 `{{variable}}` 格式
- **样式策略**：所有样式都写在 `style` 属性中，支持内联样式
- **HTML属性优先**：优先使用 `bgcolor`、`align`、`valign`、`width`、`height` 等HTML属性
- **独立表格结构**：每个组件都是独立的表格，避免嵌套问题
- **双重保险策略**：同时使用HTML属性和CSS样式确保兼容性

#### **🎨 兼容性优化**
- **腾讯SES兼容**：针对腾讯SES服务的实际支持情况优化
- **圆角效果支持**：可以使用 `border-radius` 圆角效果
- **避免阴影渐变**：不使用 `box-shadow` 和CSS渐变，使用纯色背景
- **组件间距优化**：通过外层table的垂直padding实现16px间距
- **VML背景支持**：为Outlook提供背景色支持

#### **📧 邮件客户端支持**
- **完全支持**：Gmail、Outlook、Apple Mail、Yahoo Mail、企业邮箱系统
- **渐进增强**：支持的客户端显示圆角，不支持的显示直角
- **Word居中修复**：为所有设置了max-width: 600px的表格添加align="center"属性

#### **🔧 组件库升级**
- **基础组件**：Emoji Icon、Title、Greeting、Description、Code Box
- **提示框组件**：Alert Success、Warning、Error、Info（垂直居中）
- **卡片组件**：Card、Card Success、Warning、Error、Info（圆角支持）
- **按钮组件**：Primary、Secondary、Large Button
- **辅助组件**：Badge、Divider、List、Small Text、Comment Text、Highlight Text
- **Cloud专用组件**：Task Info、Failure Reason、Deletion Info、Expiry Info
- **媒体组件**：Image（带圆角，响应式）

#### **📸 图片处理规范**
- **alt文本必须**：所有`<img>`标签必须包含有意义的alt文本
- **尺寸属性必须**：明确指定`width`和`height`属性，防止布局跳动
- **图片优化**：使用前必须压缩，减小邮件体积
- **绝对路径**：图片src必须使用绝对URL地址
- **圆角增强**：添加 `border-radius: 8px` 作为渐进增强

#### **🔒 Web内容安全网关过滤规则**
- **移除标签**：`<!DOCTYPE>`、`<html>`、`<head>`、`<body>`、`<style>`
- **忽略内容**：所有 `<style>` 标签内容被完全忽略
- **提取策略**：只提取最内层的 `<table>` 内容
- **重新包装**：将提取的内容重新包装在网关自己的 `<table>` 中
- **插入标签**：自动插入 `<tbody>` 标签
- **不兼容元素**：`<div>`、`<span>`、`<p>`、`<ul>`、`<ol>`、`<li>` 等现代HTML标签

### 📊 版本对比

| 特性 | v1版本 | v2版本 |
|------|--------|--------|
| 兼容性 | 中等 | 极高 |
| 样式支持 | 部分丢失 | 渐进增强 |
| 布局稳定性 | 一般 | 极佳 |
| 维护难度 | 中等 | 简单 |
| 海外服务器 | 样式丢失 | 完美显示 |
| 腾讯SES支持 | 不兼容 | 完全兼容 |
| 变量格式 | `${variable}` | `{{variable}}` |
| 布局方式 | div布局 | 表格布局 |
| 样式方式 | 外部CSS | 内联样式 |

### 🎯 核心改进成果
1. **100%邮件客户端兼容**：支持所有主流邮件客户端
2. **腾讯SES完美支持**：针对腾讯SES服务的专门优化
3. **渐进增强设计**：支持CSS3属性，不支持的客户端优雅降级
4. **组件化架构**：每个组件都是独立的表格，可被提取后独立工作
5. **品牌一致性**：保持Get3D品牌形象统一
6. **维护简单**：使用表格布局和内联样式，降低维护复杂度

---

## Document Overview / 文档概览

**Document Type**: Product Requirements Document (PRD)  
**Version**: 1.0  
**Last Updated**: 2025  
**Owner**: Wenwei Zheng
**Stakeholders**: Development Team, Design Team, Marketing Team  

## Subject Design Standards / Subject 设计规范

### 🎯 Subject设计原则
1. **信息优先**：优先传达关键信息，而非情绪价值
2. **简洁明了**：控制在50字符以内，避免冗长描述
3. **行动导向**：明确用户需要采取的行动
4. **场景适配**：根据业务场景选择合适的语调

### 📱 Emoji使用规则

#### **使用Emoji的场景**
- ✅ **成功/完成类**：支付成功、注册成功、任务完成等
- ✅ **提醒/警告类**：即将过期、数据清除等
- ✅ **营销/推广类**：优惠券、版本更新、用户唤醒等
- ✅ **安全/验证类**：验证码、密码重置等（使用锁🔐表示安全）
- ✅ **失败/错误类**：支付失败、任务失败等（使用警告⚠️表示问题）

#### **Emoji选择标准**
- 🎉 成功完成（支付成功、注册成功、续费成功）
- ⚡ 紧急提醒（即将过期、重要通知）
- 🎁 礼品/优惠（试用分配、优惠券、渠道商分发）
- 🔓 解锁/授权（产品授权）
- 💛 情感关怀（用户唤醒）
- 🚀 更新/升级（版本更新）
- ⏰ 时间相关（试用过期、订阅过期）
- ✨ 续费/延续（续费成功）
- 🔐 安全验证（验证码、密码重置）
- ⚠️ 警告/问题（支付失败、任务失败、数据删除）
- ✅ 任务完成（Cloud任务成功）
- 📧 邀请/协作（组织邀请、项目邀请）
- 🤝 合作/伙伴（渠道商邀请、成功）

### 🎭 语调规范

#### **严肃精炼型**（适用于安全、验证、失败类）
- 格式：`[Emoji] 核心信息`
- 示例：`🔐 Verification code`、`⚠️ Payment failed`

#### **信息明确型**（适用于高频业务场景）
- 格式：`[Emoji] 具体信息`
- 示例：`📧 ${userName} invited you to ${projectName}`、`Your ${productName} subscription expires`

#### **营销友好型**（适用于推广、唤醒类）
- 格式：`[Emoji] 吸引性描述`
- 示例：`🎁 Surprise! A special gift for you`、`💛 We miss you!`

### 📋 Subject模板规范

| 业务类型 | 语调风格 | Emoji使用 | 信息密度 | 示例格式 |
|---------|---------|-----------|---------|----------|
| **安全验证** | 严肃精炼 | 必需 | 高 | `🔐 Verification code` |
| **高频业务** | 信息明确 | 必需 | 高 | `📧 ${userName} invited you` |
| **成功完成** | 积极简洁 | 必需 | 中 | `🎉 Payment successful` |
| **提醒警告** | 关注提醒 | 必需 | 中 | `⚡ Subscription expires soon` |
| **营销推广** | 友好吸引 | 必需 | 低 | `🎁 Special gift for you` |
| **失败错误** | 简洁直接 | 必需 | 高 | `⚠️ Payment failed` |

---

## Overview / 概览
Total Templates: 31 (25 Active + 6 Deprecated) / 模板总数：31个 (25个有效 + 6个废弃)  
Active Categories: 8 / 有效分类：8个  
Base Template: `000_Base Template.html` / 基础模板：`000_Base Template.html`  
**v2版本状态**: 所有25个有效模板已完成v2版本优化，支持腾讯SES服务和100%邮件客户端兼容  

## Classification System / 分类系统

| 首字母 | 全称 | 场景描述 | 发送方式 | 备注 |
|--------|------|----------|----------|------|
| **A** | **Account** <br/> 账户管理 | 用户注册、登录、密码重置、验证码等账户相关操作 <br/> User registration, login, password reset, verification codes, etc. | 自动 / Auto | 系统触发 |
| **C** | **Cloud** <br/> Cloud业务 | Get3D Cloud产品使用：任务处理、团队邀请、项目协作、数据管理等 <br/> Get3D Cloud usage: task processing, team invitations, project collaboration, data management, etc. | 自动 / Auto | 业务流程触发 |
| **D** | **Dealer** <br/> 渠道合作 | 渠道商邀请、合作伙伴授权、经销商许可证等 <br/> Dealer invitations, partner authorizations, dealer licenses, etc. | 手动 / Manual | 商务人员操作 |
| **O** | **Order** <br/> 订单交易 | 支付成功、订单确认、计费相关 <br/> Payment success, order confirmation, billing-related | 自动 / Auto | 交易系统触发 |
| **L** | **License** <br/> 许可证管理 | 正式产品许可证授权、权限分发等 <br/> Formal product license grants, permission distribution, etc. | 混合 / Mixed | 自动+手动分发 |
| **M** | **Marketing** <br/> 营销推广 | 优惠券发放、用户激活、产品更新通知、版本发布等 <br/> Coupon distribution, user activation, product updates, version releases, etc. | 手动 / Manual | 营销活动触发 |
| **H** | **Help** <br/> 帮助服务 | 客户帮助、用户反馈处理、服务咨询等 <br/> Customer help, user feedback handling, service inquiries, etc. | 手动 / Manual | 客服人员操作 |
| **S** | **Subscription** <br/> 订阅管理 | 订阅续费、到期提醒、订阅状态变更等 <br/> Subscription renewals, expiry reminders, subscription status changes, etc. | 自动 / Auto | 订阅系统触发 |
| **T** | **Trial** <br/> 试用管理 | 试用权限分配、试用期管理、到期提醒等试用相关操作 <br/> Trial permission assignment, trial period management, expiry reminders, etc. | 混合 / Mixed | 自动+手动分发 |
| **X** | **Expired** <br/> 废弃模板 | 历史遗留模板，逐步淘汰 <br/> Legacy templates, gradually phased out | - | 不再使用 |

---

## Template Specifications / 模板规格表

| Category | New ID | Subject | Template Type | Filename | Sender Name | Variables | CTA Button | Trigger Events | Cases | Note                                                | Status |
|----------|--------|---------|---------------|----------|-------------|-----------|------------|----------------|----------------|------|--------|
| **Account** | **A01** | 🔐 Email verification | REGISTER | A01_REGISTER.html | Get3D Security | `{{register}}`, `{{code}}` | - | User registration email verification | 完成注册验证，开始使用产品 | 系统→新用户：邮箱验证，引导完成注册 | ✅ v2 |
| **Account** | **A02** | 🔐 Reset your password | RESET_PASSWORD | A02_RESET_PASSWORD.html | Get3D Security | `{{userName}}`, `{{code}}` | - | User requests password reset | 安全重置密码，恢复账户访问 | 系统→已注册用户：安全重置密码 | ✅ v2 |
| **Account** | **A03** | 🎉 Your Get3D journey begins now | SUCCESSFULLY_REGISTERED | A03_SUCCESSFULLY_REGISTERED.html | Get3D Team | `{{userName}}` | Go to My Account / `https://www.get3d.ai/personal-center/subscription` | User completes registration | 注册成功确认，获得产品访问权限 | 系统→新用户：注册成功欢迎，引导开始使用 | ✅ v2 |
| **Account** | **A04** | 🔐 Verification code | CODE | A04_CODE.html | Get3D Security | `{{code}}` | - | Sensitive operations verification | 完成身份验证，继续敏感操作 | 系统→用户：敏感操作身份验证 | ✅ v2 |
| **Cloud** | **C01** | ✅ Task {{taskName}} completed | TASK_SUCCESS | C01_TASK_SUCCESS.html | Get3D System | `{{userName}}`, `{{taskName}}`, `{{startTime}}`, `{{completionTime}}`, `{{totalDuration}}`, `{{photoCount}}`, `{{fileSize}}`, `{{modelingArea}}` | View Task / `https://www.get3d.ai/cloud/#/velora` | Task processing completed | 任务完成通知，查看处理结果 | 系统→用户：Cloud任务完成通知，引导查看结果 | ✅ v2 |
| **Cloud** | **C02** | ⚠️ Task {{taskName}} failed | TASK_FAILED | C02_TASK_FAILED.html | Get3D System | `{{userName}}`, `{{taskName}}`, `{{startTime}}`, `{{failureTime}}`, `{{processingDuration}}`, `{{photoCount}}`, `{{fileSize}}`, `{{modelingArea}}`, `{{failureReason}}` | Retry Task / `https://www.get3d.ai/cloud/#/velora` | Task processing failed | 任务失败通知，了解原因并重试 | 系统→用户：Cloud任务失败通知，引导重试 | ✅ v2 |
| **Cloud** | **C03** | 📧 Team invitation from {{userName}} | ORG_INVITE | C03_ORG_INVITE.html | Get3D Team | `{{userName}}`, `{{teamName}}`, `{{inviteLink}}` | Join Now / `{{inviteLink}}` | Organization invitation sent | 获得组织邀请，加入团队协作 | 用户→被邀请用户：组织邀请，促进团队协作 | ✅ v2 |
| **Cloud** | **C04** | 📧 Project invitation from {{userName}} | PROJECT_INVITE | C04_PROJECT_INVITE.html | Get3D Team | `{{userName}}`, `{{orgName}}`, `{{projectName}}`, `{{inviteLink}}` | Join Project / `{{inviteLink}}` | Project invitation sent | 获得项目邀请，参与项目协作（默认viewer权限） | 用户→被邀请用户：项目邀请，促进项目协作 | ✅ v2 |
| **Cloud** | **C05** | ⚠️ Cloud data expires soon | DATA_EXPIRY_WARNING | C05_DATA_EXPIRY_WARNING.html | Get3D System | `{{userName}}`, `{{endDate}}`, `{{deletionDate}}`, `{{gracePeriodDays}}` | Upgrade Plan / `https://www.get3d.ai/personal-center/subscription` | Data approaching expiry | 数据即将清除警告，及时备份数据 | 系统→用户：Cloud数据即将清除警告，提醒备份 | ✅ v2 |
| **Cloud** | **C06** | ⚠️ Cloud data deleted | DATA_DELETED | C06_DATA_DELETED.html | Get3D System | `{{userName}}`, `{{deletionDate}}`, `{{gracePeriodDays}}` | Start New Trial / `https://www.get3d.ai/personal-center/subscription` | Trial data permanently deleted after grace period | 数据已清除通知，重新开始使用 | 系统→用户：Cloud数据已清除通知，引导重新开始 | ✅ v2 |
| **Dealer** | **D01** | 🤝 Get3D partnership invitation | DEALER | D01_DEALER.html | Get3D Partnership | `{{userName}}`, `{{dealerName}}`, `{{inviteLink}}` | Join Partnership / `{{inviteLink}}` | Dealer invitation sent | 获得渠道商合作机会，建立商业关系 | 运营→潜在渠道商：渠道商邀请，建立合作关系 | ✅ v2 |
| **Dealer** | **D02** | 🎉 Welcome to Get3D partnership | DEALER_SUCCESS | D02_DEALER_SUCCESS.html | Get3D Partnership | `{{userName}}`, `{{dealerName}}`, `{{dealerInfo}}` | Access Portal / `https://www.get3d.ai/personal-center/subscription` | Dealer registration completed | 渠道商身份确认，获得分发权限 | 系统→新渠道商：渠道商注册成功确认，激活合作伙伴权限 | ✅ v2 |
| **Dealer** | **D03** | 🏆 License from {{dealerName}} | DEALER_DISTRIBUTION | D03_DEALER_DISTRIBUTION.html | Get3D Licensing | `{{userName}}`, `{{dealerName}}`, `{{productName}}`, `{{distributionDate}}`, `{{validDate}}` | Go to My Account / `https://www.get3d.ai/personal-center/subscription` | Dealer distributed license to end user | 获得渠道商分发的产品授权 | 系统→终端用户：渠道商分发授权通知，激活产品服务 | ✅ v2 |
| **Help** | **H01** | 📧 Contact inquiry from {{fullName}} | CONTACT_US | H01_CONTACT_US.html | Get3D Support | `{{fullName}}`, `{{email}}`, `{{company}}`, `{{topic}}`, `{{submittedDate}}`, `{{message}}` | Reply to Customer / `mailto:{{email}}?subject=Re: {{topic}} - Get3D Inquiry` | Contact form submitted | 内部通知，运营人员及时处理客户咨询 | 系统→运营人员：客户咨询通知，展示用户提交的完整信息 | ✅ v2 |
| **License** | **L01** | 🏆 You received a {{productName}} license | PRODUCT_LICENSE_GRANTED | L01_PRODUCT_LICENSE_GRANTED.html | Get3D Licensing | `{{userName}}`, `{{productName}}`, `{{licenseType}}`, `{{activationDate}}`, `{{expiryDate}}` | Go to My Account / `https://www.get3d.ai/personal-center/subscription` | Manual license grant by operations | 正式产品授权激活，解锁高级功能 | 运营→用户：运营人员手动授权，激活正式产品功能 | ✅ v2 |
| **Marketing** | **M01** | 🎁 Exclusive coupon for you | COUPON_GRANTED | M01_COUPON_GRANTED.html | Get3D Offers | `{{userName}}`, `{{discountAmount}}`, `{{expiryDate}}`, `{{validProducts}}` | Shop Now / `https://www.get3d.ai/pricing` | Coupon issued | 获得专属优惠券，享受折扣优惠 | 运营→用户：优惠券发放，促进购买转化 | ✅ v2 |
| **Marketing** | **M02** | 💛 We miss you! | USER_AWAKENING | M02_USER_AWAKENING.html | Get3D Team | `{{userName}}`, `{{activityInfo}}` | Explore Now / `https://www.get3d.ai/personal-center/subscription` | Inactive user re-engagement | 重新激活账户，发现新功能 | 运营→不活跃用户：用户唤醒，重新激活使用 | ✅ v2 |
| **Marketing** | **M03** | 🚀 {{productName}} {{version}} update | VERSION_UPDATE | M03_VERSION_UPDATE.html | Get3D Team | `{{userName}}`, `{{productName}}`, `{{version}}`, `{{updateInfo}}` | Discover Features / `https://www.get3d.ai/personal-center/subscription` | New version released | 版本更新通知，体验新功能 | 运营→用户：版本更新通知，引导体验新功能 | ✅ v2 |
| **Order** | **O01** | 🎉 Payment successful! | BUY_SUCCESS | O01_BUY_SUCCESS.html | Get3D Billing | `{{userName}}`, `{{orderNumber}}`, `{{productName}}`, `{{paymentAmount}}`, `{{currency}}`, `{{purchaseTime}}` | Go to My Account / `https://www.get3d.ai/personal-center/subscription` | Payment completed | 支付成功确认，产品服务激活 | 系统→用户：支付成功确认，激活产品服务 | ✅ v2 |
| **Order** | **O02** | ⚠️ Payment failed | PAYMENT_FAILURE | O02_PAYMENT_FAILED.html | Get3D Billing | `{{userName}}`, `{{orderNumber}}`, `{{productName}}`, `{{paymentAmount}}`, `{{currency}}`, `{{paymentMethod}}` | Update Payment / `https://www.get3d.ai/personal-center/subscription` | Auto-renewal failed | 支付失败通知，更新支付方式 | 系统→用户：支付失败通知，引导更新支付方式 | ✅ v2 |
| **Subscription** | **S01** | ⏰ {{productName}} expires soon | SUBSCRIPTION_EXPIRING | S01_SUBSCRIPTION_EXPIRING.html | Get3D Billing | `{{userName}}`, `{{productName}}`, `{{planName}}`, `{{daysLeft}}`, `{{expiryDate}}`, `{{nextBillingDate}}`, `{{amount}}` | Renew Subscription / `https://www.get3d.ai/personal-center/subscription` | Subscription ending | 订阅即将过期提醒，及时续费 | 系统→用户：订阅即将过期提醒，促进续费 | ✅ v2 |
| **Subscription** | **S02** | ⚠️ {{productName}} expired | SUBSCRIPTION_EXPIRED | S02_SUBSCRIPTION_EXPIRED.html | Get3D Billing | `{{userName}}`, `{{productName}}`, `{{endDate}}` | Buy Now / `https://www.get3d.ai/personal-center/subscription` | Subscription ended | 订阅已过期通知，重新购买服务 | 系统→用户：订阅过期通知，引导重新购买 | ✅ v2 |
| **Subscription** | **S03** | ⚠️ {{productName}} cancelled | SUBSCRIPTION_CANCELLED | S03_SUBSCRIPTION_CANCELLED.html | Get3D Billing | `{{userName}}`, `{{productName}}`, `{{cancelDate}}`, `{{endDate}}`, `{{reason}}` | Reactivate / `{{cta_link}}` | Subscription cancelled | 订阅取消确认，了解取消原因 | 系统→用户：订阅取消确认，引导重新订阅 | ✅ v2 |
| **Subscription** | **S04** | 🎉 {{productName}} renewed | RENEWAL_SUCCESS | S04_RENEWAL_SUCCESS.html | Get3D Billing | `{{userName}}`, `{{productName}}`, `{{nextBillingDate}}`, `{{amount}}`, `{{currency}}` | Go to My Account / `https://www.get3d.ai/personal-center/subscription` | Renewal successful | 续费成功确认，服务继续使用 | 系统→用户：续费成功确认，感谢用户忠诚度 | ✅ v2 |
| **Trial** | **T01** | 🎁 {{productName}} trial ready | TRIAL_ASSIGNMENT | T01_TRIAL_ASSIGNMENT.html | Get3D Team | `{{userName}}`, `{{productName}}`, `{{trialDuration}}`, `{{startDate}}`, `{{expiryDate}}` | Go to My Account / `https://www.get3d.ai/personal-center/subscription` | Trial assignment | 试用权限分配，开始免费体验 | 系统/运营→用户：试用权限分配，引导开始体验 | ✅ v2 |
| **Trial** | **T02** | ⏰ {{productName}} trial expires soon | TRIAL_EXPIRING | T02_TRIAL_EXPIRING.html | Get3D Billing | `{{userName}}`, `{{productName}}`, `{{daysLeft}}`, `{{expiryDate}}`, `{{projectCount}}` | Upgrade Now / `https://www.get3d.ai/personal-center/subscription` | Trial period ending | 试用即将过期提醒，考虑购买 | 系统→用户：试用即将过期提醒，促进转化 | ✅ v2 |
| **Trial** | **T03** | ⏰ {{productName}} trial expired | TRIAL_EXPIRED | T03_TRIAL_EXPIRED.html | Get3D Billing | `{{userName}}`, `{{productName}}`, `{{expiryDate}}` | Buy Now / `https://www.get3d.ai/personal-center/subscription` | Trial period ended | 试用已过期通知，购买正式版 | 系统→用户：试用过期通知，引导购买 | ✅ v2 |
| **Expired** | **X01** | Apply audit | APPLY_FAILED | X01_APPLY_FAILED.html | Get3D Team | `userName`, `applyName`, `failureReason` | Contact Support / `${cta_link}` | Application rejected | 申请被拒绝通知，了解拒绝原因 | 系统→用户：申请被拒绝通知（已废弃） | ⚠️ |
| **Expired** | **X02** | Apply audit | APPLY_SUCCESS | X02_APPLY_SUCCESS.html | Get3D Team | `userName`, `applyName` | View Details / `${cta_link}` | Application approved | 申请通过通知，查看详细信息 | 系统→用户：申请通过通知（已废弃） | ⚠️ |
| **Expired** | **X03** | Congratulations on Being Shortlisted! Notification of Finalization for The First International Smart City Real 3D Modeling Technology Innovation Application Competition | WORKS_SUCCESS | X03_WORKS_SUCCESS.html | Get3D System | `userName`, `workName` | View Work / `${cta_link}` | Work processing completed | 作品完成通知，查看处理结果 | 系统→用户：作品完成通知（已废弃） | ⚠️ |
| **Expired** | **X04** | Notification of Shortlisting Failure | WORKS_FAILED | X04_WORKS_FAILED.html | Get3D System | `userName`, `workName`, `failureReason` | Retry / `${cta_link}` | Work processing failed | 作品失败通知，了解原因并重试 | 系统→用户：作品失败通知（已废弃） | ⚠️ |
| **Expired** | **X05** | Your 3D model is published successfully | PUBLISH_SUCCESS | X05_PUBLISH_SUCCESS.html | Get3D System | `userName`, `publishName` | View Published / `${cta_link}` | Content published | 内容发布成功通知，查看发布结果 | 系统→用户：内容发布成功通知（已废弃） | ⚠️ |
| **Expired** | **X06** | Your 3D model is published failed | PUBLISH_FAILED | X06_PUBLISH_FAILED.html | Get3D System | `userName`, `publishName`, `failureReason` | Retry Publish / `${cta_link}` | Content publish failed | 内容发布失败通知，了解原因并重试 | 系统→用户：内容发布失败通知（已废弃） | ⚠️ |
| **Expired** | **X07** | ⚠️ Your ${productName} subscription suspended | SUBSCRIPTION_SUSPENDED | X07_LICENSE_GRANTED.html | Get3D Licensing | `userName`, `productName`, `licenseInfo`, `licenseType`, `activationDate`, `expiryDate` | Start Using / `${cta_link}` | License granted | 产品授权激活，解锁高级功能 | 系统→用户：产品授权通知（已废弃） | ⚠️ |

### Category Legend / 分类说明
- **A**: Account (账户管理) - 4个模板
- **C**: Cloud (Cloud业务) - 6个模板  
- **D**: Dealer (渠道合作) - 3个模板
- **H**: Help (帮助服务) - 1个模板
- **L**: License (许可证管理) - 1个模板
- **M**: Marketing (营销推广) - 3个模板
- **O**: Order (订单交易) - 2个模板
- **S**: Subscription (订阅管理) - 4个模板
- **T**: Trial (试用管理) - 3个模板
- **X**: Expired (废弃模板) - 6个模板

---

## Business Process Flow / 业务流程图

### Get3D Complete Email Notification Business Process / Get3D完整邮件通知业务流程

以下流程图展示了Get3D平台中所有涉及邮件通知的业务场景，包括4类参与者的交互和33个邮件模板的触发时机。

```mermaid
graph TB
    %% 定义参与者
    subgraph Platform ["🏢 平台方"]
        ADMIN["🔧 后台系统"]
        STAFF["👨‍💼 运营人员"]
    end
    
    subgraph External ["👥 外部用户"]
        USER["👤 终端用户"]
        DEALER["🤝 渠道商"]
    end

    %% 用户生命周期起点
    START([新用户访问平台]) --> REG_FORM[用户填写注册表单]
    
    %% 账户管理流程 (Account - A类)
    subgraph Account ["🔐 账户管理流程"]
        REG_FORM --> A01{{📧 A01: 邮箱验证码<br/>📨 发送给→ 👤用户}}
        A01 --> VERIFY{👤用户验证成功}
        VERIFY -->|是| A03{{📧 A03: 注册成功<br/>📨 发送给→ 👤用户}}
        VERIFY -->|否| A01
        
        %% 忘记密码流程
        LOGIN_FAIL[👤用户登录失败] --> A02{{📧 A02: 重置密码<br/>📨 发送给→ 👤用户}}
        A02 --> RESET_PWD[👤用户重置密码成功]
        
        %% 敏感操作验证
        SENSITIVE_OP[👤用户敏感操作] --> A04{{📧 A04: 验证码<br/>📨 发送给→ 👤用户}}
        A04 --> OP_SUCCESS[👤用户操作完成]
    end

    %% 试用管理流程 (Trial - T类)
    subgraph Trial ["🎁 试用管理"]
        A03 --> AUTO_TRIAL[🔧系统自动分配试用]
        AUTO_TRIAL --> T01{{📧 T01: 试用分配<br/>📨 发送给→ 👤用户}}
        
        STAFF --> MANUAL_TRIAL[👨‍💼运营手动分配试用]
        MANUAL_TRIAL --> T01
        
        T01 --> TRIAL_USE[👤用户试用期使用]
        TRIAL_USE --> TRIAL_WARNING{🔧系统检测试用即将过期}
        TRIAL_WARNING --> T02{{📧 T02: 试用即将过期<br/>📨 发送给→ 👤用户}}
        
        T02 --> TRIAL_DECISION{👤用户决策}
        TRIAL_DECISION -->|购买| PURCHASE[👤用户购买]
        TRIAL_DECISION -->|忽略| T03{{📧 T03: 试用过期<br/>📨 发送给→ 👤用户}}
    end

    %% 许可证管理 (License - L类)
    subgraph License ["🔓 许可证管理"]
        STAFF --> MANUAL_LICENSE[👨‍💼运营手动授权]
        MANUAL_LICENSE --> L01{{📧 L01: 产品授权<br/>📨 发送给→ 👤用户}}
    end

    %% 订单交易流程 (Order - O类)
    subgraph Order ["💰 订单交易流程"]
        PURCHASE --> PAY_PROCESS[🔧支付处理]
        PAY_PROCESS --> PAY_SUCCESS{支付成功}
        PAY_SUCCESS -->|是| O01{{📧 O01: 支付成功<br/>📨 发送给→ 👤用户}}
        PAY_SUCCESS -->|否| PAY_RETRY[重试支付]
        
        O01 --> PURCHASE_SUCCESS[购买成功]
    end

    %% 订阅管理流程 (Subscription - S类)
    subgraph Subscription ["🔄 订阅管理"]
        PURCHASE_SUCCESS --> SUB_ACTIVE[🔧订阅激活]
        SUB_ACTIVE --> SUB_USE[👤用户订阅期使用]
        
        SUB_USE --> SUB_WARNING{🔧系统检测订阅即将过期}
        SUB_WARNING --> S01{{📧 S01: 订阅即将过期<br/>📨 发送给→ 👤用户}}
        
        S01 --> SUB_DECISION{👤用户决策}
        SUB_DECISION -->|自动续费| AUTO_RENEW[🔧自动续费]
        SUB_DECISION -->|手动取消| S03{{📧 S03: 订阅取消<br/>📨 发送给→ 👤用户}}
        SUB_DECISION -->|忽略| S02{{📧 S02: 订阅过期<br/>📨 发送给→ 👤用户}}
        
        AUTO_RENEW --> RENEW_SUCCESS{续费成功}
        RENEW_SUCCESS -->|是| S04{{📧 S04: 续费成功<br/>📨 发送给→ 👤用户}}
        RENEW_SUCCESS -->|否| O02{{📧 O02: 续费失败<br/>📨 发送给→ 👤用户}}
        
        O02 --> RETRY_PAY[🔧重试支付]
        RETRY_PAY --> FINAL_FAIL{最终失败}
        FINAL_FAIL -->|是| S02{{📧 S02: 订阅过期<br/>📨 发送给→ 👤用户}}
        FINAL_FAIL -->|否| S04
        
        S04 --> SUB_USE
    end

    %% Cloud业务流程 (Cloud - C类)
    subgraph Cloud ["☁️ Cloud业务流程"]
        SUB_ACTIVE --> CLOUD_ACCESS[👤用户Cloud服务访问]
        CLOUD_ACCESS --> TASK_SUBMIT[👤用户提交建模任务]
        
        TASK_SUBMIT --> TASK_PROCESS[🔧系统任务处理]
        TASK_PROCESS --> TASK_RESULT{任务结果}
        TASK_RESULT -->|成功| C01{{📧 C01: 任务成功<br/>📨 发送给→ 👤用户}}
        TASK_RESULT -->|失败| C02{{📧 C02: 任务失败<br/>📨 发送给→ 👤用户}}
        
        %% 团队协作
        USER --> INVITE_ORG[👤用户邀请加入组织]
        INVITE_ORG --> C03{{📧 C03: 组织邀请<br/>📨 发送给→ 👤被邀请用户}}
        
        USER --> INVITE_PROJECT[👤用户邀请加入项目]
        INVITE_PROJECT --> C04{{📧 C04: 项目邀请<br/>📨 发送给→ 👤被邀请用户}}
        
        %% 数据管理
        T03 --> DATA_GRACE[🔧系统启动14天宽容期]
        DATA_GRACE --> C05{{📧 C05: 数据即将清除<br/>📨 发送给→ 👤用户}}
        C05 --> GRACE_END{🔧宽容期结束}
        GRACE_END --> C06{{📧 C06: 数据已清除<br/>📨 发送给→ 👤用户}}
    end

    %% 渠道商流程 (Dealer - D类)
    subgraph Dealer ["🤝 渠道商流程"]
        %% 渠道商招募
        STAFF --> DEALER_INVITE[运营邀请成为渠道商]
        DEALER_INVITE --> D01{{📧 D01: 渠道商邀请<br/>📨 发送给→ 🤝渠道商}}
        
        D01 --> DEALER_ACCEPT[🤝渠道商接受邀请]
        DEALER_ACCEPT --> DEALER_AUTH[🤝渠道商认证]
        DEALER_AUTH --> D02{{📧 D02: 渠道商成功确认<br/>📨 发送给→ 🤝渠道商}}
        
        %% 渠道商分发业务
        D02 --> DEALER_HAS_DISTRIBUTION_ABILITY[🤝渠道商已具备分发能力]
        DEALER_HAS_DISTRIBUTION_ABILITY --> DEALER_PURCHASE_SKU[🤝渠道商在shop页面购买SKU产品]
        DEALER_PURCHASE_SKU --> DEALER_SPECIFY_EMAIL[🤝渠道商指定账户邮箱进行分发]
        DEALER_SPECIFY_EMAIL --> DEALER_DISTRIBUTE[🤝渠道商完成分发操作]
        
        %% 关键：系统发邮件，不是渠道商发
        DEALER_DISTRIBUTE --> SYSTEM_SEND_NOTICE[🔧后台系统发送通知]
        SYSTEM_SEND_NOTICE --> D03{{📧 D03: 渠道商分发授权<br/>📨 发送给→ 👤终端用户<br/>💬内容:ABC科技为您开通了}}
        
        D03 --> ENDUSER_RECEIVE[👤终端用户收到授权]
    end

    %% 营销推广 (Marketing - M类)
    subgraph Marketing ["📢 营销推广"]
        STAFF --> COUPON_ISSUE[👨‍💼运营发放优惠券]
        COUPON_ISSUE --> M01{{📧 M01: 优惠券发放<br/>📨 发送给→ 👤用户}}
        
        STAFF --> VERSION_RELEASE[👨‍💼运营版本更新发布]
        VERSION_RELEASE --> M03{{📧 M03: 版本更新通知<br/>📨 发送给→ 👤用户}}
        
        STAFF --> USER_INACTIVE{👨‍💼运营检测不活跃用户}
        USER_INACTIVE --> M02{{📧 M02: 用户唤醒<br/>📨 发送给→ 👤不活跃用户}}
    end

    %% 客户服务 (Help - H类)
    subgraph Help ["🆘 客户服务"]
        USER --> CONTACT_FORM[👤用户填写联系表单]
        CONTACT_FORM --> H01{{📧 H01: 客户咨询通知<br/>📨 发送给→ 👨‍💼运营人员}}
        H01 --> STAFF_REPLY[👨‍💼客服回复处理]
    end

    %% 样式定义
    classDef emailNode fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000
    classDef actorNode fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef processNode fill:#e8f5e8,stroke:#388e3c,stroke-width:1px,color:#000
    classDef decisionNode fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000
    classDef reuseNode fill:#ffebee,stroke:#d32f2f,stroke-width:2px,color:#000

    %% 应用样式
    class A01,A02,A03,A04,T01,T02,T03,C01,C02,C03,C04,C05,C06,D01,D02_DEALER,D02_ENDUSER,O01,O02,L01,M01,M02,M03,H01,S01,S02,S03,S04 emailNode
    class ADMIN,STAFF,USER,DEALER actorNode
    class VERIFY,TRIAL_DECISION,SUB_DECISION,RENEW_SUCCESS,FINAL_FAIL,TASK_RESULT,GRACE_END,PAY_SUCCESS,TRIAL_WARNING,SUB_WARNING,USER_INACTIVE processNode
    class D02_ENDUSER reuseNode
```

### 流程图说明 / Process Flow Explanation

#### **🎯 邮件模板触发统计**
- **Account (A类)**: 4个模板 - 覆盖用户注册、验证、密码管理全流程
- **Cloud (C类)**: 6个模板 - 覆盖任务处理、团队协作、数据管理
- **Dealer (D类)**: 3个模板 - 支持渠道商邀请、成功确认和分发通知
- **Help (H类)**: 1个模板 - 覆盖客户服务反馈
- **License (L类)**: 1个模板 - 覆盖正式产品授权
- **Marketing (M类)**: 3个模板 - 覆盖优惠券、用户激活、版本更新
- **Order (O类)**: 2个模板 - 覆盖支付成功和失败处理
- **Subscription (S类)**: 4个模板 - 覆盖订阅全生命周期管理
- **Trial (T类)**: 3个模板 - 覆盖试用权限分配、期限管理、到期处理

#### **⚡ 关键业务节点**
1. **注册验证** → A01 → A03: 用户生命周期起点
2. **试用分配** → T01: 可由系统自动或运营手动触发
3. **购买支付** → O01: 集成服务激活，避免重复通知
4. **订阅续费** → S04/O02: 自动续费成功/失败处理
5. **数据清理** → C05 → C06: Cloud特有的14天宽容期机制
6. **渠道分发** → D01 → D02 → D03: 支持渠道商邀请、成功确认和批量分发给终端用户

#### **👥 参与者职责和邮件接收者**
- **🔧 后台系统**: 自动触发A、C、O、S类邮件，负责渠道商分发通知的发送
- **👨‍💼 运营人员**: 手动触发D、M、L类邮件和客服处理，不直接接收邮件
- **👤 终端用户**: 接收几乎所有邮件（A、C、O、S、L、M、H类），主动触发H类联系
- **🤝 渠道商**: 接收D类邮件（邀请、授权），触发分发动作但不直接发邮件给终端用户。

#### **🔄 渠道商分发流程详解**
1. **👨‍💼运营邀请渠道商** → D01邮件发送给🤝渠道商
2. **🤝渠道商认证成功** → D02邮件发送给🤝渠道商（成功确认）
3. **🤝渠道商成为渠道商后即具备分发能力**
4. **🤝渠道商在shop页面购买需要分发的SKU产品**
5. **🤝渠道商指定终端用户邮箱进行分发**
6. **🔧后台系统检测到分发动作** → D03邮件发送给👤终端用户（内容："ABC科技为您开通了..."）

**关键**: 所有邮件都由Get3D后台系统发送，渠道商只是触发分发动作，不直接发送邮件给终端用户。

---

## Database Structure / 数据结构
```json
{
    "variables": [
        "userName", 
        "productName", 
        "memberType", 
        "renewalDate", 
        "amount", 
        "currency", 
        "endDate", 
        "nextBillingDate"
    ],
    "category": "subscription",
    "image": "pay.png"
}
```

### Variable Dependencies Summary / 变量依赖关系总结

#### **按用户状态分类的变量可用性**

| 用户状态 | 可用变量 | 不可用变量 | 适用模板 | 处理策略 |
|---------|---------|-----------|---------|---------|
| **未注册用户** | `mail` | `userName` | A01, A04 | 使用 "Dear User" 或邮箱前缀 |
| **注册中用户** | `mail`, `code` | `userName` | A01, A04 | 使用 "Dear User" 或邮箱前缀 |
| **已注册用户** | `userName`, `mail` | - | A02, A03, C01-C06, O01-O02, L01, M01-M03, S01-S04 | 完整个性化 |
| **邀请场景** | `mail`, 可能有 `userName` | - | C03-C05, D01 | 条件判断显示 |
| **联系反馈** | `topic`, `fullName`, `email`, `company`, `message` | - | H01 | 使用fullName，无需userName |

#### **按模板类型分类的变量要求**

| 模板类型 | 必需变量 | 可选变量 | 回退策略 |
|---------|---------|---------|---------|
| **账户类 (A01-A04)** | `mail`, 可能有`code` | `userName` | "Dear User" |
| **Cloud业务 (C01-C06)** | `userName`, `mail` | 业务相关变量 | 无需回退 |
| **渠道合作 (D01-D02)** | `userName`, `mail`, `dealerName` | - | 无需回退 |
| **订单交易 (O01-O02)** | `userName`, `mail` | 订单相关变量 | 无需回退 |
| **许可证管理 (L01)** | `userName`, `mail` | 许可证相关变量 | 无需回退 |
| **营销推广 (M01-M03)** | `userName`, `mail` | 营销相关变量 | 无需回退 |
| **帮助服务 (H01)** | `topic`, `fullName`, `email`, `company`, `message` | - | 使用fullName |
| **订阅管理 (S01-S04)** | `userName`, `mail` | 订阅相关变量 | 无需回退 |

#### **关键业务场景的变量处理**

| 业务场景 | 变量状态 | 处理方式 | 示例 |
|---------|---------|---------|------|
| **用户注册** | 只有邮箱 | 使用通用称呼 | "Dear User, please verify..." |
| **忘记密码** | 有用户名和邮箱 | 个性化称呼 | "Dear John, reset your password..." |
| **Cloud邀请未注册用户** | 只有邮箱 | 友好的通用称呼 | "Hi there, you're invited to join..." |
| **Cloud邀请已注册用户** | 有用户名和邮箱 | 个性化称呼 | "Hi John, you're invited to join..." |
| **匿名用户联系** | 邮箱+消息 | 根据是否登录判断 | 条件显示用户名 |
| **SKU包购买** | 有用户名和完整订单信息 | 显示包含产品列表 | "包含：Mapper + ModelFun + Viewer VIP" |
| **渠道商分发给终端用户** | 有渠道商和终端用户信息 | 说明来源和授权方 | "ABC科技为您开通了Mapper Pro" |
| **Cloud数据清除** | 有数据集和时间信息 | 强调宽容期和不可恢复 | "14天宽容期已到，数据已永久清除" |

---

## Variable Naming Standards / 变量命名规范

### Variable Format Specification / 变量格式规范

**统一格式 / Unified Format**: `{{variable_name}}`

```html
<!-- ✅ 正确格式 -->
<p>Dear {{userName}},</p>
<p>Your order {{orderNumber}} is confirmed.</p>
<a href="{{cta_link}}">View Order</a>
<img src="{{image}}" alt="Icon">

<!-- ❌ 避免使用 -->
<p>Dear ${userName},</p>     <!-- 不使用单括号 -->
<p>Dear %userName%,</p>       <!-- 不使用百分号 -->
<p>Dear [userName],</p>       <!-- 不使用方括号 -->
```

**技术实现原理 / Technical Implementation**:

- 后端使用正则表达式 `/\{\{(\w+)\}\}/g` 匹配和替换变量
- 与数据库存储格式保持一致
- 支持双括号模板字符串标准

### Standard Variable Names / 标准变量名称
基于数据库现有命名规范，所有变量使用 camelCase 格式：

#### **User Information / 用户信息**
- `userName` - 用户名称
- `mail` - 用户邮箱 (系统自动填充)
- `dealerName` - 供应商/经销商名称 (本质上也是userName，但需要独立标识)

#### **Product & Service / 产品与服务**
- `productName` - 产品名称
- `productType` - 产品类型 ("mapper" | "modelfun" | "viewer" | "cloud" | "package")
- `planType` - 订阅类型 (试用、订阅、标准版、专业版等，英文)
- `licenseInfo` - 许可证信息
- `dataSetName` - Cloud中用户建立项目后建模的数据任务名称
- `packageContents` - SKU包内容列表 (数组格式，如 ["Mapper", "ModelFun", "Viewer VIP"])
- `packageDescription` - SKU包描述说明
- `specialTerms` - 特殊条款说明 (如 "Viewer VIP为一年有效期")

#### **Time & Date / 时间与日期**
- `trialEndDate` - 试用结束日期
- `endDate` - 结束日期
- `subStartTime` - 订阅开始时间
- `subEndTime` - 订阅结束时间
- `nextBillingDate` - 下次计费日期
- `renewalDate` - 续费日期
- `cancelDate` - 取消日期
- `suspendDate` - 暂停日期
- `purchaseTime` - 购买时间
- `completionTime` - 完成时间
- `expiryTime` - 过期时间
- `deletionDate` - 删除日期
- `gracePeriodDays` - 宽容期天数 (如14天)

#### **Financial / 财务相关**
- `orderNumber` - 订单编号
- `paymentAmount` - 支付金额
- `amount` - 金额
- `currency` - 货币类型
- `paymentMethod` - 支付方式

#### **Content & Tasks / 内容与任务**
- `taskName` - 任务名称
- `workName` - 作品名称
- `publishName` - 发布内容名称
- `dataSetName` - Cloud中数据任务名称
- `message` - 消息内容
- `topic` - 用户反馈主题
- `fullName` - 用户反馈中的全名
- `email` - 用户反馈中的邮箱
- `company` - 用户反馈中的公司名称

#### **System & Technical / 系统与技术**
- `code` - 验证码
- `version` - 版本号
- `updateInfo` - 更新信息
- `activityInfo` - 活动信息

#### **Invitations & Partnerships / 邀请与合作**
- `orgName` - 组织名称
- `projectName` - 项目名称
- `dealerName` - 经销商名称
- `applyName` - 申请名称
- `recipientType` - 接收者类型 ("dealer" | "end_user")
- `distributedBy` - 分发来源说明 (如 "您的合作伙伴ABC科技为您开通了以下服务")
- `sourceDealer` - 来源渠道商名称

#### **Links & Actions / 链接与操作**
- `cta_link` - 主要操作链接
- `inviteLink` - 邀请链接
- `resetLink` - 重置链接
- `replyLink` - 回复链接

#### **Failure & Reasons / 失败与原因**
- `failureReason` - 失败原因
- `reason` - 取消/暂停原因

#### **Promotional / 促销相关**
- `couponCode` - 优惠券代码

---

## Variable Dependencies Analysis / 变量依赖关系分析

### Business Context Analysis / 业务场景分析
根据不同的业务场景，系统可获取的用户信息存在差异。以下是详细的变量依赖关系分析：

#### **Authentication Phase / 认证阶段**

**A01 - REGISTER (注册邮箱验证)**
- **业务场景**: 新用户注册时的邮箱验证，用户处于兴奋期，需要欢迎和鼓励
- **可用变量**: `mail`, `code`, `cta_link`
- **不可用变量**: `userName` (用户名在验证后才创建)
- **内容特点**: 友好欢迎语调，突出产品价值，引导用户完成注册
- **特殊处理**: 邮件称呼使用 "Hi there" 或 "欢迎您"

**A02 - RESET_PASSWORD (重置密码)**
- **业务场景**: 已注册用户忘记密码，用户可能焦虑，需要安全感和清晰指引
- **可用变量**: `userName`, `mail`, `resetLink`
- **内容特点**: 强调安全性，提供安全提示，语调严肃可靠
- **说明**: 此时用户已完成注册，系统可获取完整用户信息

**A03 - SUCCESSFULLY_REGISTERED (注册成功)**
- **业务场景**: 用户完成邮箱验证，账户创建成功
- **可用变量**: `userName`, `mail`, `cta_link`
- **说明**: 用户刚创建用户名，系统可获取完整信息

**A04 - CODE (通用身份验证码)**
- **业务场景**: 敏感操作身份验证，用户处于任务期，希望快速高效完成验证
- **可用变量**: `mail`, `code`, `expiryTime`, `cta_link`
- **可能变量**: `userName` (取决于是否已登录)
- **内容特点**: 简洁高效，直切主题，强调时效性
- **说明**: 通用验证码模板，支持各种敏感操作的身份确认

#### **Cloud Business Phase / Cloud业务阶段**

**C01 - TASK_SUCCESS (任务成功)**
- **业务场景**: 已登录用户的任务处理完成
- **可用变量**: `userName`, `mail`, `taskName`, `completionTime`, `cta_link`
- **说明**: 用户已登录，所有信息可用

**C02 - TASK_FAILED (任务失败)**
- **业务场景**: 已登录用户的任务处理失败
- **可用变量**: `userName`, `mail`, `taskName`, `failureReason`, `cta_link`
- **说明**: 用户已登录，所有信息可用

**C03 - ORG_INVITE (组织邀请)**
- **业务场景**: Get3D Cloud中邀请用户加入组织
- **可用变量**: `userName`, `mail`, `orgName`, `inviteLink`
- **说明**: Cloud业务功能，被邀请用户可能已注册或未注册，需要判断处理

**C04 - ORG_PROJECT_INVITE (组织项目邀请)**
- **业务场景**: Get3D Cloud中邀请用户加入组织的特定项目
- **可用变量**: `userName`, `mail`, `orgName`, `projectName`, `inviteLink`
- **说明**: Cloud业务功能，通常邀请已注册用户

**C05 - PROJECT_INVITE (项目邀请)**
- **业务场景**: Get3D Cloud中邀请用户加入项目
- **可用变量**: `userName`, `mail`, `projectName`, `inviteLink`
- **说明**: Cloud业务功能，通常邀请已注册用户

**C06 - DATA_EXPIRY_WARNING (数据过期警告)**
- **业务场景**: 警告已注册用户Cloud数据即将过期
- **可用变量**: `userName`, `mail`, `dataSetName`, `endDate`, `cta_link`
- **说明**: 用户已注册，数据集信息完整

**C07 - DATA_DELETED (数据永久清除)**
- **业务场景**: Cloud试用过期14天宽容期结束，数据已被永久清除
- **可用变量**: `userName`, `mail`, `dataSetName`, `deletionDate`, `gracePeriodDays`, `cta_link`
- **说明**: 强调宽容期已到，数据无法恢复，引导用户重新开始或购买服务

#### **Order Transaction Phase / 订单交易阶段**

**O01 - BUY_SUCCESS (购买成功)**
- **业务场景**: 已登录用户完成支付，包含服务激活确认
- **可用变量**: `userName`, `mail`, `orderNumber`, `paymentAmount`, `currency`, `purchaseTime`, `productName`, `paymentMethod`, `productType`, `packageContents`, `packageDescription`, `specialTerms`, `cta_link`
- **说明**: 用户已登录并完成支付，包含完整的订单和支付信息。SKU包购买时会显示包含的具体产品和特殊条款

**O02 - PAYMENT_FAILED (支付失败)**
- **业务场景**: 自动续费失败
- **可用变量**: `userName`, `mail`, `productName`, `failureReason`, `cta_link`
- **说明**: 用户已注册，包含支付失败原因

#### **Dealer & Partnership Phase / 经销商合作阶段**

**D01 - DEALER (经销商邀请)**
- **业务场景**: 邀请成为经销商
- **可用变量**: `userName`, `mail`, `dealerName`, `inviteLink`
- **说明**: 可能邀请已注册用户或潜在合作伙伴

**D02 - DEALER_LICENSE_GRANTED (经销商许可证授权)**
- **业务场景**: 经销商许可证激活，或渠道商向终端用户分发授权
- **可用变量**: `userName`, `mail`, `dealerName`, `licenseInfo`, `recipientType`, `distributedBy`, `sourceDealer`, `cta_link`
- **说明**: 支持两种场景：1) 经销商获得授权 2) 渠道商向终端用户分发产品授权

#### **License Management Phase / 许可证管理阶段**

#### **Trial Management Phase / 试用管理阶段**

**T01 - TRIAL_ASSIGNMENT (试用分配)**
- **业务场景**: 为已注册用户分配试用权限
- **可用变量**: `userName`, `mail`, `planType`, `trialEndDate`, `cta_link`
- **说明**: 用户已注册，包含试用权限信息

**T02 - TRIAL_EXPIRING (试用即将过期)**
- **业务场景**: 已注册用户的试用期即将结束
- **可用变量**: `userName`, `mail`, `trialEndDate`, `cta_link`
- **说明**: 用户已注册，信息完整

**T03 - TRIAL_EXPIRED (试用已过期)**
- **业务场景**: 已注册用户的试用期已结束
- **可用变量**: `userName`, `mail`, `endDate`, `cta_link`
- **说明**: 用户已注册，信息完整

#### **License Management Phase / 许可证管理阶段**

**L01 - PRODUCT_LICENSE_GRANTED (产品许可证授权)**
- **业务场景**: 运营人员手动授权产品许可证（测试账号、补发授权、先用后付等）
- **可用变量**: `userName`, `mail`, `productName`, `licenseInfo`, `licenseType`, `activationDate`, `expiryDate`, `cta_link`
- **说明**: 运营人员手动操作，正式商业授权流程的一部分

#### **Marketing & Promotion Phase / 营销推广阶段**

**M01 - COUPON_GRANTED (优惠券发放)**
- **业务场景**: 向已注册用户发放优惠券
- **可用变量**: `userName`, `mail`, `couponCode`, `expiryDate`, `cta_link`
- **说明**: 用户已注册，包含优惠券信息

**M02 - USER_AWAKENING (用户唤醒)**
- **业务场景**: 唤醒不活跃的已注册用户
- **可用变量**: `userName`, `mail`, `activityInfo`, `cta_link`
- **说明**: 用户已注册但长时间未活跃，信息完整

**M03 - VERSION_UPDATE (版本更新)**
- **业务场景**: 通知已注册用户系统版本更新
- **可用变量**: `userName`, `mail`, `version`, `updateInfo`, `cta_link`
- **说明**: 用户已注册，包含版本更新信息

#### **Help Service Phase / 帮助服务阶段**

**H01 - CONTACT_US (联系我们)**
- **业务场景**: 用户通过联系表单提交问题，系统通知内部运营人员
- **可用变量**: `topic`, `fullName`, `email`, `company`, `message`, `submittedDate`
- **说明**: 内部通知邮件，展示用户提交的完整咨询信息，供运营人员及时处理

#### **Subscription Management Phase / 订阅管理阶段**

**S01 - SUBSCRIPTION_EXPIRING (订阅即将过期)**
- **业务场景**: 已注册用户的订阅即将结束
- **可用变量**: `userName`, `mail`, `endDate`, `cta_link`
- **说明**: 用户已注册，订阅信息完整

**S02 - SUBSCRIPTION_EXPIRED (订阅已过期)**
- **业务场景**: 已注册用户的订阅已结束
- **可用变量**: `userName`, `mail`, `endDate`, `cta_link`
- **说明**: 用户已注册，订阅信息完整

**S03 - SUBSCRIPTION_CANCELLED (订阅取消)**
- **业务场景**: 用户主动取消订阅
- **可用变量**: `userName`, `mail`, `productName`, `planType`, `cancelDate`, `reason`, `endDate`, `cta_link`
- **说明**: 用户已注册，包含完整的取消信息

**S04 - RENEWAL_SUCCESS (续费成功)**
- **业务场景**: 订阅自动续费成功
- **可用变量**: `userName`, `mail`, `productName`, `nextBillingDate`, `amount`, `currency`, `cta_link`
- **说明**: 用户已注册，包含续费信息



### Variable Fallback Strategy / 变量回退策略

#### **用户名称处理策略**
1. **有用户名时**: 使用 `userName`
2. **无用户名时**: 使用以下回退方案
   - 中文邮件: "亲爱的用户"
   - 英文邮件: "Dear User"
   - 或使用邮箱前缀: `userEmail.split('@')[0]`

#### **邮件个性化等级**
- **Level 1**: 仅邮箱地址 (注册阶段)
- **Level 2**: 邮箱 + 用户名 (正常用户)
- **Level 3**: 完整用户信息 (VIP用户、经销商等)

#### **模板变量验证**
在发送邮件前，系统应验证：
1. 必需变量是否存在
2. 可选变量的回退处理
3. 变量格式是否正确

### Implementation Guidelines / 实施指南

#### **后端API调用规范**
```javascript
// 注册验证邮件 - 欢迎新用户，只有邮箱
sendEmail({
    template: 'A01_REGISTER',
    to: mail,
    variables: {
        register: mail,
        code: verificationCode,
        cta_link: verificationLink
    }
});

// 重置密码邮件 - 安全重置，有用户名和邮箱
sendEmail({
    template: 'A02_RESET_PASSWORD',
    to: mail,
    variables: {
        userName: userName,
        register: mail,
        code: verificationCode,
        cta_link: resetPasswordLink
    }
});

// 敏感操作验证邮件 - 通用身份验证
sendEmail({
    template: 'A04_CODE',
    to: mail,
    variables: {
        register: mail,
        userName: userName || null, // 可能有也可能没有
        code: verificationCode,
        expiryTime: '5分钟',
        cta_link: verificationLink
    }
});

// 支付成功邮件 - 完整订单信息
sendEmail({
    template: 'O01_BUY_SUCCESS',
    to: mail,
    variables: {
        userName: userName,
        register: mail,
        orderNumber: orderNumber,
        paymentAmount: paymentAmount,
        currency: currency,
        purchaseTime: purchaseTime,
        productName: productName,
        paymentMethod: paymentMethod,
        productType: productType, // "mapper" | "modelfun" | "viewer" | "cloud" | "package"
        packageContents: packageContents, // SKU包时有效: ["Mapper", "ModelFun", "Viewer VIP"]
        packageDescription: packageDescription, // SKU包描述
        specialTerms: specialTerms, // 特殊条款，如 "Viewer VIP为一年有效期"
        cta_link: orderDetailLink
    }
});

// SKU包购买成功示例
sendEmail({
    template: 'O01_BUY_SUCCESS',
    to: mail,
    variables: {
        userName: "John Doe",
        productName: "Software Package",
        productType: "package",
        packageContents: ["Mapper Pro", "ModelFun", "Viewer VIP"],
        packageDescription: "完整3D建模解决方案",
        specialTerms: "Viewer VIP为一年有效期，其他为永久授权",
        paymentAmount: 4550,
        currency: "USD",
        cta_link: orderDetailLink
    }
});

// 用户反馈邮件 - 内部通知运营人员
sendEmail({
    template: 'H01_CONTACT_US',
    to: '<EMAIL>', // 发送给内部运营人员
    variables: {
        topic: topic,
        fullName: fullName,
        email: email,
        company: company,
        message: message,
        submittedDate: new Date().toLocaleString()
    }
});

// 运营人员手动授权产品许可证
sendEmail({
    template: 'L01_PRODUCT_LICENSE_GRANTED',
    to: mail,
    variables: {
        userName: userName,
        register: mail,
        productName: productName,
        licenseInfo: licenseKey,
        licenseType: "Permanent", // 或 "Subscription", "Trial"
        activationDate: new Date().toLocaleDateString(),
        expiryDate: expiryDate || "Permanent",
        cta_link: activationLink
    }
});

// 渠道商向终端用户分发授权
sendEmail({
    template: 'D03_DEALER_DISTRIBUTION',
    to: endUserMail,
    variables: {
        userName: endUserName,
        dealerName: dealerName,
        productName: productName,
        licenseInfo: licenseKey,
        distributedBy: `您的合作伙伴${dealerName}为您开通了以下服务`,
        sourceDealer: dealerName,
        cta_link: activationLink
    }
});

// Cloud数据永久清除通知
sendEmail({
    template: 'C06_DATA_DELETED',
    to: mail,
    variables: {
        userName: userName,
        dataSetName: "我的3D建模项目",
        deletionDate: "2024-01-15",
        gracePeriodDays: 14,
        cta_link: newTrialLink
    }
});

// 订阅取消邮件 - 完整订阅信息
sendEmail({
    template: 'S03_SUBSCRIPTION_CANCELLED',
    to: mail,
    variables: {
        userName: userName,
        register: mail,
        productName: productName,
        planType: planType,
        cancelDate: cancelDate,
        reason: reason,
        endDate: endDate,
        cta_link: resubscribeLink
    }
});

// 试用分配邮件 - 运营手动分配或系统自动分配
sendEmail({
    template: 'T01_TRIAL_ASSIGNMENT',
    to: mail,
    variables: {
        userName: userName,
        register: mail,
        planType: planType,
        trialEndDate: trialEndDate,
        cta_link: trialActivationLink
    }
});

// 试用即将过期邮件 - 自动提醒
sendEmail({
    template: 'T02_TRIAL_EXPIRING',
    to: mail,
    variables: {
        userName: userName,
        register: mail,
        trialEndDate: trialEndDate,
        cta_link: upgradeLink
    }
});

// 试用过期邮件 - 自动通知
sendEmail({
    template: 'T03_TRIAL_EXPIRED',
    to: mail,
    variables: {
        userName: userName,
        register: mail,
        endDate: endDate,
        cta_link: buyNowLink
    }
});

// 正式产品授权邮件 - 运营分发
sendEmail({
    template: 'L01_PRODUCT_LICENSE_GRANTED',
    to: mail,
    variables: {
        userName: userName,
        register: mail,
        productName: productName,
        licenseInfo: licenseInfo,
        cta_link: activationLink
    }
});

// 支付失败邮件 - 自动续费失败
sendEmail({
    template: 'O02_PAYMENT_FAILED',
    to: mail,
    variables: {
        userName: userName,
        register: mail,
        productName: productName,
        failureReason: failureReason,
        cta_link: updatePaymentLink
    }
});
```

#### **前端模板处理**
```html
<!-- A01 注册验证 - 欢迎友好型 -->
<h1>🎉 欢迎加入Get3D！</h1>
<p>Hi there, 您离开启创意3D之旅只有一步之遥！</p>
<p>请使用以下验证码完成邮箱验证：</p>
<div class="verification-code welcome">{{code}}</div>
<p>验证码10分钟内有效。验证成功后，您将可以免费试用所有产品！</p>

<!-- A02 重置密码 - 安全可靠型 -->
<h1>🔒 密码重置请求</h1>
<p>Dear {{userName}}, 我们收到了您账户的密码重置请求。</p>
<p><strong>如果这不是您本人操作，请立即联系客服。</strong></p>
<a href="{{cta_link}}" class="security-button">安全重置密码</a>
<p>链接将在24小时后失效。请在安全环境下完成操作。</p>

<!-- A04 通用验证 - 简洁高效型 -->
<h1>🔐 身份验证</h1>
<p>Dear {{userName || 'User'}}, 请验证您的身份以继续操作。</p>
<div class="verification-code simple">{{code}}</div>
<p>验证码{{expiryTime}}内有效，请尽快完成验证。</p>

<!-- 标准变量替换格式 -->
<p>Dear {{userName || 'User'}},</p>
<p>Your email: {{register}}</p>

<!-- 联系我们模板的特殊处理 -->
<p>Dear {{fullName}},</p>
<p>Thank you for contacting us about: {{topic}}</p>
<p>Company: {{company}}</p>

<!-- 订阅相关模板 -->
<p>Dear {{userName}},</p>
<p>Your {{planType}} subscription for {{productName}} has been cancelled.</p>
<p>Cancellation Date: {{cancelDate}}</p>
<p>Reason: {{reason}}</p>

<!-- SKU包购买成功模板 -->
<p>Dear {{userName}},</p>
<p>Your {{productName}} purchase is complete!</p>
{{#if productType === 'package'}}
<div class="package-details">
  <h3>Your Package Includes:</h3>
  <ul>
    {{#each packageContents}}
    <li>{{this}}</li>
    {{/each}}
  </ul>
  <p class="package-description">{{packageDescription}}</p>
  {{#if specialTerms}}
  <p class="special-terms">Note: {{specialTerms}}</p>
  {{/if}}
</div>
{{/if}}

<!-- 渠道商分发给终端用户模板 -->
{{#if recipientType === 'end_user'}}
<p>Dear {{userName}},</p>
<p>{{distributedBy}}</p>
<p>Product: {{productName}}</p>
<p>Provided by: {{sourceDealer}}</p>
{{else}}
<p>Dear {{dealerName}},</p>
<p>Your dealer license for {{productName}} is now active.</p>
{{/if}}

<!-- 数据永久清除警告 -->
<p>Dear {{userName}},</p>
<p>Your dataset "{{dataSetName}}" has been permanently deleted on {{deletionDate}}.</p>
<p>This action was taken after the {{gracePeriodDays}}-day grace period expired.</p>
<p class="warning">This data cannot be recovered.</p>

<!-- 数据过期警告 -->
<p>Dear {{userName}},</p>
<p>Your dataset "{{dataSetName}}" will expire on {{endDate}}.</p>
```

#### **变量验证和错误处理**
```javascript
// 后端发送前验证
function validateEmailVariables(template, variables) {
    const requiredVars = getRequiredVariables(template);
    const missingVars = requiredVars.filter(var => !variables[var]);
    
    if (missingVars.length > 0) {
        // 应用回退策略
        applyFallbackStrategy(template, variables, missingVars);
    }
    
    return variables;
}

// 回退策略实现
function applyFallbackStrategy(template, variables, missingVars) {
    if (missingVars.includes('userName') && variables.register) {
        // 使用邮箱前缀或默认称呼
        variables.userName = variables.register.split('@')[0];
        // 或者
        // variables.userName = 'User';
    }
    
    // 处理联系我们模板的特殊情况
    if (template === 'H01_CONTACT_US' && !variables.fullName && variables.email) {
        variables.fullName = variables.email.split('@')[0];
    }
}

// 变量替换实现
function replaceVariables(template, variables) {
    return template.replace(/\{\{(\w+)\}\}/g, (match, varName) => {
        return variables[varName] || match; // 保留未匹配的变量
    });
}

// 处理试用相关邮件的特殊情况
if (template === 'T01_TRIAL_ASSIGNMENT' && !variables.planType) {
    variables.planType = 'Free Trial'; // 默认试用类型
}

// 处理支付失败邮件的特殊情况
if (template === 'O02_PAYMENT_FAILED' && !variables.failureReason) {
    variables.failureReason = 'Payment method expired'; // 默认值示例
}
```

---

## Quality Assurance / 质量保证

### Testing Requirements / 测试要求
1. **Variable Replacement Testing / 变量替换测试**
   - 所有变量必须正确替换
   - 缺失变量的错误处理
   - 特殊字符和HTML转义测试

2. **Email Client Compatibility / 邮件客户端兼容性**
   - Gmail, Outlook, Apple Mail
   - 移动端和桌面端显示
   - 深色模式和浅色模式

3. **Performance Testing / 性能测试**
   - 模板加载速度
   - 图片加载优化
   - 大量发送时的系统性能

### Success Metrics / 成功指标
- 邮件发送成功率 > 99%
- 模板渲染错误率 < 0.1%

---

## Maintenance & Updates / 维护与更新

### Regular Reviews / 定期审查
- 每季度审查模板性能和用户反馈
- 年度全面评估和优化

### Change Management / 变更管理
- 所有模板变更必须通过产品经理审批
- 重大变更需要A/B测试验证
- 保持版本控制和变更日志

### Documentation Updates / 文档更新
- 及时更新变量命名规范
- 维护最新的实施指南

## 📧 Sender Identity Standards / 发送者身份规范

### **Sender身份分类标准**

#### **🔧 系统自动邮件 (System Automated)**
- **Get3D System**: 系统自动触发的技术通知
  - Cloud任务处理结果 (C01, C02)
  - 数据管理通知 (C05, C06)
  - 系统级技术通知

#### **🔐 安全验证邮件 (Security & Verification)**
- **Get3D Security**: 账户安全相关邮件
  - 邮箱验证 (A01, A04)
  - 密码重置 (A02)
  - 敏感操作验证

#### **💰 财务交易邮件 (Billing & Payment)**
- **Get3D Billing**: 支付和订阅相关邮件
  - 支付成功/失败 (O01, O02)
  - 订阅管理 (S01, S02, S03, S04)
  - 试用到期提醒 (T02, T03)

#### **🏆 授权许可邮件 (License & Authorization)**
- **Get3D Licensing**: 产品授权相关邮件
  - 正式产品授权 (L01)
  - 渠道商分发授权 (D03)

#### **🤝 商业合作邮件 (Partnership & Business)**
- **Get3D Partnership**: 渠道商合作相关邮件
  - 渠道商邀请 (D01)
  - 渠道商成功确认 (D02)

#### **🎁 营销推广邮件 (Marketing & Promotion)**
- **Get3D Offers**: 优惠券和促销邮件
  - 优惠券发放 (M01)

#### **📧 用户服务邮件 (User Service)**
- **Get3D Team**: 用户服务和产品相关邮件
  - 注册成功欢迎 (A03)
  - 试用分配 (T01)
  - 用户唤醒 (M02)
  - 版本更新 (M03)
  - 团队邀请 (C03, C04)

#### **🆘 客户支持邮件 (Customer Support)**
- **Get3D Support**: 客户服务相关邮件
  - 客户咨询通知 (H01)

### **Sender选择原则**

1. **功能导向**: 根据邮件功能选择对应Sender
2. **用户认知**: 让用户清楚知道邮件来源和目的
3. **品牌一致**: 保持Get3D品牌形象统一
4. **职责明确**: 区分系统自动和人工操作邮件

### **Sender使用总结表**

| Sender身份 | 使用场景 | 模板数量 | 代表模板 |
|-----------|---------|---------|---------|
| **Get3D System** | 系统自动技术通知 | 6个 | C01, C02, C05, C06 |
| **Get3D Security** | 账户安全验证 | 3个 | A01, A02, A04 |
| **Get3D Billing** | 支付订阅财务 | 8个 | O01, O02, S01-S04, T02, T03 |
| **Get3D Licensing** | 产品授权许可 | 2个 | L01, D03 |
| **Get3D Partnership** | 渠道商合作 | 2个 | D01, D02 |
| **Get3D Offers** | 优惠券促销 | 1个 | M01 |
| **Get3D Team** | 用户服务产品 | 6个 | A03, T01, M02, M03, C03, C04 |
| **Get3D Support** | 客户服务支持 | 1个 | H01 |

### **Sender使用场景说明**

#### **🔧 Get3D System (6个模板)**
- **C01, C02**: Cloud任务处理结果通知
- **C05, C06**: Cloud数据管理通知
- **X03-X06**: 废弃的系统通知模板

#### **🔐 Get3D Security (3个模板)**
- **A01, A04**: 邮箱验证和敏感操作验证
- **A02**: 密码重置验证

#### **💰 Get3D Billing (8个模板)**
- **O01, O02**: 支付成功/失败通知
- **S01-S04**: 订阅管理全流程通知
- **T02, T03**: 试用到期提醒

#### **🏆 Get3D Licensing (2个模板)**
- **L01**: 正式产品授权通知
- **D03**: 渠道商分发授权通知

#### **🤝 Get3D Partnership (2个模板)**
- **D01**: 渠道商邀请
- **D02**: 渠道商成功确认

#### **🎁 Get3D Offers (1个模板)**
- **M01**: 优惠券发放

#### **📧 Get3D Team (6个模板)**
- **A03**: 注册成功欢迎
- **T01**: 试用分配
- **M02**: 用户唤醒
- **M03**: 版本更新
- **C03, C04**: 团队/项目邀请

#### **🆘 Get3D Support (1个模板)**
- **H01**: 客户咨询通知

---

## Template Specifications / 模板规格表