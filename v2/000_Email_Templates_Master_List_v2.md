# Get3D Email Templates Master List v2

## 📋 模板规范说明

### 🎯 设计原则："腾讯SES优化"邮件设计
- **"腾讯SES兼容"策略**：针对腾讯SES服务的优化设计
- **"组件间距优化"理念**：通过外层table的垂直padding实现组件间距
- **"渐进增强"原则**：支持圆角和style内联样式，但避免阴影和渐变
- **"表格布局为王"**：使用 `<table>` 替代 `<div>` 布局
- **内联样式支持**：所有样式都写在 `style` 属性中
- **HTML属性优先**：优先使用 `bgcolor`、`align`、`valign`、`width`、`height` 等HTML属性
- **圆角支持**：可以使用 `border-radius` 圆角效果
- **避免阴影渐变**：不使用 `box-shadow` 和CSS渐变，使用纯色背景

### 🏗️ 技术规范
- **文档类型**：XHTML 1.0 Transitional
- **字体**：Arial, Helvetica, sans-serif（系统字体）
- **最大宽度**：600px
- **背景色**：#f2f2f2（外层），#ffffff（内容区），#e4e4e4（Footer）
- **主色调**：#d8ff18（Get3D绿），#1a1a1a（深灰）
- **变量格式**：使用双括号 `{{variable}}` 格式
- **组件间距**：通过外层table的 `cellpadding="16"` 和 `style="padding: 16px;"` 实现

### 🎨 样式支持规范
基于腾讯SES服务的实际支持情况：
- ✅ **完全支持**：`style` 内联样式、`border-radius` 圆角效果
- ✅ **HTML属性**：`bgcolor`、`align`、`valign`、`width`、`height`、`cellpadding`、`cellspacing`
- ✅ **CSS属性**：`padding`、`margin`、`background-color`、`color`、`font-size`、`font-weight`、`border-radius`
- ❌ **不支持**：`box-shadow` 阴影效果、CSS渐变（`linear-gradient`、`radial-gradient`）
- ✅ **替代方案**：使用纯色背景和边框替代阴影和渐变效果

### 🔒 Web内容安全网关过滤规则
基于实际测试发现的过滤行为：
- **移除标签**：`<!DOCTYPE>`、`<html>`、`<head>`、`<body>`、`<style>`
- **忽略内容**：所有 `<style>` 标签内容被完全忽略
- **提取策略**：只提取最内层的 `<table>` 内容
- **重新包装**：将提取的内容重新包装在网关自己的 `<table>` 中
- **插入标签**：自动插入 `<tbody>` 标签
- **不兼容元素**：`<div>`、`<span>`、`<p>`、`<ul>`、`<ol>`、`<li>` 等现代HTML标签

### 📸 图片处理规范
- **alt文本必须**：所有`<img>`标签必须包含有意义的alt文本
- **尺寸属性必须**：明确指定`width`和`height`属性，防止布局跳动
- **图片优化**：使用前必须压缩（如TinyPNG），减小邮件体积
- **绝对路径**：图片src必须使用绝对URL地址（https://...）
- **圆角增强**：添加 `border-radius: 8px` 作为渐进增强

### 📧 模板结构
```
Header (Get3D Logo)
├── Main Content
│   ├── Emoji Icon + Title
│   ├── Greeting
│   ├── Description
│   ├── Code Box (验证码)
│   ├── Alert Components (提示框)
│   ├── CTA Buttons (按钮)
│   ├── Card Components (卡片)
│   ├── Badge Components (徽章)
│   ├── Divider (分割线)
│   ├── List Components (列表)
│   ├── Text Components (文本)
│   ├── Image Components (图片)
│   ├── Cloud Components (云服务组件)
│   └── Content Area (实际内容区域)
└── Footer (Contact Info)
```

### 🎨 间距规范
- **组件间距**：通过外层table的 `cellpadding="16"` 和 `style="padding: 16px;"` 实现
- **分割线**：只有上下间距 `padding: 1px 0;`，无左右内边距
- **按钮优化**：外层16px间距，按钮本身无内边距，避免叠加
- **卡片内容**：标题和内容之间8px间距，避免过度嵌套

### 🔧 兼容性优化
- **垂直居中**：提示框文字使用 `valign="middle"` 实现垂直居中
- **双重保险**：同时使用HTML属性和CSS样式确保兼容性
- **VML支持**：为Outlook提供背景色支持
- **独立表格**：每个组件都是独立的表格，避免嵌套问题

---

## 📁 模板分类

### 🔐 A类：账户验证 (Account Verification)
| 编号 | 文件名 | 用途 | 状态 |
|------|--------|------|------|
| A01 | A01_REGISTER.html | 注册验证码 | ✅ 已完成 |
| A02 | A02_RESET_PASSWORD.html | 重置密码验证码 | ✅ 已完成 |
| A03 | A03_SUCCESSFULLY_REGISTERED.html | 注册成功通知 | ✅ 已完成 |
| A04 | A04_CODE.html | 通用验证码 | ✅ 已完成 |

### ☁️ C类：云服务 (Cloud Services)
| 编号 | 文件名 | 用途 | 状态 |
|------|--------|------|------|
| C01 | C01_TASK_SUCCESS.html | 任务完成通知 | ✅ 已完成 |
| C02 | C02_TASK_FAILED.html | 任务失败通知 | ✅ 已完成 |
| C03 | C03_ORG_INVITE.html | 组织邀请 | ✅ 已完成 |
| C04 | C04_PROJECT_INVITE.html | 项目邀请 | ✅ 已完成 |
| C05 | C05_DATA_EXPIRY_WARNING.html | 数据过期警告 | ✅ 已完成 |
| C06 | C06_DATA_DELETED.html | 数据删除通知 | ✅ 已完成 |

### 🛒 D类：经销商 (Dealer)
| 编号 | 文件名 | 用途 | 状态 |
|------|--------|------|------|
| D01 | D01_DEALER.html | 经销商邀请 | ✅ 已完成 |
| D02 | D02_DEALER_SUCCESS.html | 经销商申请成功 | ✅ 已完成 |
| D03 | D03_DEALER_DISTRIBUTION.html | 经销商分发授权 | ✅ 已完成 |

### 📞 H类：联系 (Contact)
| 编号 | 文件名 | 用途 | 状态 |
|------|--------|------|------|
| H01 | H01_CONTACT_US.html | 联系我们 | ✅ 已完成 |

### 📜 L类：许可证 (License)
| 编号 | 文件名 | 用途 | 状态 |
|------|--------|------|------|
| L01 | L01_PRODUCT_LICENSE_GRANTED.html | 产品许可证授予 | ✅ 已完成 |

### 🎁 M类：营销 (Marketing)
| 编号 | 文件名 | 用途 | 状态 |
|------|--------|------|------|
| M01 | M01_COUPON_GRANTED.html | 优惠券发放 | ✅ 已完成 |
| M02 | M02_USER_AWAKENING.html | 用户唤醒 | ✅ 已完成 |
| M03 | M03_VERSION_UPDATE.html | 版本更新 | ✅ 已完成 |

### 🛒 O类：订单 (Order)
| 编号 | 文件名 | 用途 | 状态 |
|------|--------|------|------|
| O01 | O01_BUY_SUCCESS.html | 购买成功 | ✅ 已完成 |
| O02 | O02_PAYMENT_FAILED.html | 支付失败 | ✅ 已完成 |

### 📅 S类：订阅 (Subscription)
| 编号 | 文件名 | 用途 | 状态 |
|------|--------|------|------|
| S01 | S01_SUBSCRIPTION_EXPIRING.html | 订阅即将过期 | ✅ 已完成 |
| S02 | S02_SUBSCRIPTION_EXPIRED.html | 订阅已过期 | ✅ 已完成 |
| S03 | S03_SUBSCRIPTION_CANCELLED.html | 订阅已取消 | ✅ 已完成 |
| S04 | S04_RENEWAL_SUCCESS.html | 续费成功 | ✅ 已完成 |

### 🆓 T类：试用 (Trial)
| 编号 | 文件名 | 用途 | 状态 |
|------|--------|------|------|
| T01 | T01_TRIAL_ASSIGNMENT.html | 试用分配 | ✅ 已完成 |
| T02 | T02_TRIAL_EXPIRING.html | 试用即将过期 | ✅ 已完成 |
| T03 | T03_TRIAL_EXPIRED.html | 试用已过期 | ✅ 已完成 |

---

## 🎨 组件库

### 📝 基础组件
- **Emoji Icon**：大图标显示
- **Title**：主标题
- **Greeting**：问候语
- **Description**：描述文本
- **Code Box**：验证码框（带圆角）

### 🎯 提示框组件
- **Alert Success**：成功提示框（绿色背景，垂直居中）
- **Alert Warning**：警告提示框（黄色背景，垂直居中）
- **Alert Error**：错误提示框（红色背景，垂直居中）
- **Alert Info**：信息提示框（蓝色背景，垂直居中）

### 📋 卡片组件
- **Card**：基础卡片（白色背景，圆角）
- **Card Success**：成功卡片（绿色左边框）
- **Card Warning**：警告卡片（橙色左边框）
- **Card Error**：错误卡片（红色左边框）
- **Card Info**：信息卡片（蓝色左边框）

### 🎯 按钮组件
- **Primary Button**：主要按钮（Get3D绿色背景）
- **Secondary Button**：次要按钮（较小尺寸）
- **Large Button**：大按钮（较大尺寸）

### 🏷️ 辅助组件
- **Badge**：状态徽章（ACTIVE、PENDING、EXPIRED、PROCESSING）
- **Divider**：分割线（1px高度，16px上下间距）
- **List**：列表（使用`<br>`分隔）
- **Small Text**：小文本（14px，灰色）
- **Comment Text**：注释文本（12px，更浅灰色）
- **Highlight Text**：高亮文本（带颜色强调）

### ☁️ Cloud专用组件
- **Task Info**：任务信息展示（绿色边框）
- **Failure Reason**：失败原因（红色背景）
- **Deletion Info**：删除信息（红色边框）
- **Expiry Info**：过期信息（橙色边框）

### 📸 媒体组件
- **Image**：图片组件（带圆角，响应式）

---

## 🔧 变量说明

### 👤 用户相关
- `{{userName}}` - 用户名
- `{{fullName}}` - 全名
- `{{email}}` - 邮箱地址
- `{{register}}` - 注册时的邮箱地址

### 🔐 验证相关
- `{{code}}` - 验证码

### 🏢 组织相关
- `{{teamName}}` - 团队名称
- `{{orgName}}` - 组织名称
- `{{projectName}}` - 项目名称
- `{{inviter}}` - 邀请人

### 📦 产品相关
- `{{productName}}` - 产品名称
- `{{version}}` - 版本号
- `{{licenseKey}}` - 许可证密钥

### 💰 商业相关
- `{{amount}}` - 金额
- `{{couponCode}}` - 优惠券代码
- `{{discountAmount}}` - 折扣金额
- `{{dealerName}}` - 经销商名称

### ⏰ 时间相关
- `{{expiryDate}}` - 过期日期
- `{{startTime}}` - 开始时间
- `{{completionTime}}` - 完成时间
- `{{deletionDate}}` - 删除日期

### 🔗 链接相关
- `{{cta_link}}` - 主要操作链接
- `{{primary_link}}` - 主要链接
- `{{secondary_link}}` - 次要链接

### ☁️ 云服务相关
- `{{taskName}}` - 任务名称
- `{{failureReason}}` - 失败原因

---

## 📱 兼容性说明

### ✅ 支持的邮件客户端
- **Gmail** (网页版和移动端)
- **Outlook** (2019/365)
- **Apple Mail**
- **Yahoo Mail**
- **企业邮箱系统**

### 🎨 渐进增强特性
- **圆角效果**：支持的客户端显示圆角，不支持的显示直角
- **纯色背景**：使用纯色背景替代渐变
- **边框效果**：使用边框替代阴影效果
- **透明度**：使用纯色替代
- **文本变换**：移除 `text-transform: uppercase`，使用大写字母
- **字间距**：移除 `letter-spacing`，确保兼容性

### 🔍 腾讯SES兼容性验证
基于腾讯SES服务的实际支持情况：
- ✅ **完全支持**：margin, padding, background-color, color, font-size, font-weight, font-family, line-height, text-decoration, border, width, height, text-align, vertical-align, display: inline-block, border-radius
- ✅ **渐进增强**：border-radius（支持的客户端显示圆角，不支持的显示直角）
- ❌ **不支持**：box-shadow（阴影效果）、CSS渐变（linear-gradient、radial-gradient）
- ✅ **替代方案**：使用纯色背景和边框替代阴影和渐变效果

### 🎨 腾讯SES优化策略
基于腾讯SES服务的实际限制：
- ✅ **style内联样式支持**：可以使用完整的CSS样式
- ✅ **圆角效果支持**：可以使用 `border-radius` 圆角效果
- ✅ **组件间距优化**：通过外层table的垂直padding实现16px间距
- ✅ **HTML属性优先**：继续使用 `bgcolor`、`align`、`valign` 等HTML属性
- ✅ **独立表格结构**：每个组件都是独立的表格，避免嵌套问题
- ✅ **双重保险策略**：同时使用HTML属性和CSS样式确保兼容性
- ❌ **避免阴影渐变**：不使用 `box-shadow` 和CSS渐变，使用纯色背景

### 🔧 技术特性
- **表格布局**：100%兼容
- **内联样式**：最大兼容性
- **系统字体**：确保显示
- **MSO支持**：Outlook兼容
- **VML背景**：Outlook背景色支持
- **独立组件**：每个组件都是独立的表格结构

---

## 📋 使用指南

### 1. 模板选择
根据邮件类型选择对应的模板类别和编号。

### 2. 变量替换
将模板中的双括号变量占位符 `{{variable}}` 替换为实际值。

### 3. 内容定制
根据具体需求调整邮件内容，保持品牌一致性。

### 4. 测试验证
在多个邮件客户端中测试显示效果，特别关注腾讯SES的过滤效果。

### 5. 发送部署
使用腾讯SES服务发送邮件。

---

## 🚀 更新日志

### v2.2 (2025-01-XX) - "腾讯SES优化"邮件设计
- ✅ **腾讯SES兼容策略**：针对腾讯SES服务的优化设计
- ✅ **圆角效果支持**：可以使用 `border-radius` 圆角效果
- ✅ **style内联样式支持**：可以使用完整的CSS样式
- ✅ **组件间距优化**：通过外层table的垂直padding实现16px间距
- ✅ **避免阴影渐变限制**：不使用 `box-shadow` 和CSS渐变，使用纯色背景
- ✅ **优化间距实现**：通过 `cellpadding="16"` 和 `style="padding: 16px;"` 实现组件间距
- ✅ **保持HTML属性优先**：继续使用 `bgcolor`、`align`、`valign` 等HTML属性
- ✅ **独立表格结构**：每个组件都是独立的表格，避免嵌套问题
- ✅ **双重保险策略**：同时使用HTML属性和CSS样式确保兼容性

### v2.1 (2025-01-XX) - "堡垒级"邮件设计
- ✅ **"堡垒级"防御策略**：针对Web内容安全网关的严格过滤机制
- ✅ **"家具设计"理念**：每个组件都是独立的"家具"，可被提取后独立工作
- ✅ **Web内容安全网关过滤规则**：基于实际测试发现的过滤行为
- ✅ **HTML属性优先策略**：优先使用 `bgcolor`、`align`、`valign`、`width`、`height` 等HTML属性
- ✅ **变量格式更新**：统一使用双括号 `{{variable}}` 格式
- ✅ **Footer背景色更新**：统一使用 `#e4e4e4` 背景色
- ✅ **圆角渐进增强**：恢复所有组件的 `border-radius` 属性
- ✅ **内边距优化**：修复所有组件的内边距叠加问题
- ✅ **垂直居中对齐**：修复提示框文字的垂直居中对齐
- ✅ **组件间距统一**：所有组件之间统一16px间距
- ✅ **分割线优化**：分割线只有1px上下间距，无左右内边距
- ✅ **按钮内边距修复**：避免按钮和外层td的内边距叠加
- ✅ **独立表格结构**：每个组件都是独立的表格，避免嵌套问题
- ✅ **双重保险策略**：同时使用HTML属性和CSS样式确保兼容性
- ✅ **VML背景支持**：为Outlook提供背景色支持

### v2.0 (2025-01-XX)
- ✅ 采用"回到1999年"设计原则
- ✅ 使用表格布局替代div布局
- ✅ 所有样式内联化
- ✅ 移除外部CSS依赖
- ✅ 增强邮件客户端兼容性
- ✅ 支持渐进增强特性
- ✅ 基于Can I Email专业兼容性数据优化
- ✅ 实施渐进增强策略：border-radius、Web Fonts、悬停效果
- ✅ 针对腾讯云新加坡服务器严格审查机制优化
- ✅ `<style>` 标签放在 `<body>` 内避免被过滤
- ✅ 修复所有`<p>`、`<div>`和`<span>`标签，确保100%表格布局
- ✅ 移除字段名后的冒号，优化表格显示
- ✅ 调整表格字段和值的宽度比例为40:60
- ✅ 统一变量命名规范，移除别名
- ✅ 增加图片处理规范和无障碍访问原则
- ✅ 完善Outlook条件注释说明
- ✅ 统一验证码变量为`${code}`，注册邮箱为`${register}`
- ✅ 为所有组件添加渐进式圆角（验证码框、提示框、卡片、状态徽章、列表、Cloud组件）
- ✅ 添加图片处理规范示例（alt文本、尺寸属性、圆角、绝对路径）
- ✅ 完善MSO条件注释示例
- ✅ 修复验证码框圆角位置和任务信息组件圆角缺失问题
- ✅ **关键兼容性修复**：将所有table上的margin替换为间距表格，确保Outlook兼容性
- ✅ **列表组件修复**：将td上的margin-bottom替换为padding-bottom
- ✅ **cellpadding确认**：确认cellpadding="0"是正确的，可以作为padding的平替使用
- ✅ **所有margin修复完成**：已修复所有组件的margin问题，包括基础卡片、成功卡片、警告卡片、错误卡片、信息卡片、状态徽章、分割线、列表、小文本、注释文本、高亮文本、图片示例、任务信息、失败原因、删除信息、过期信息等所有组件
- ✅ **cellpadding确认**：确认cellpadding="0"是正确的，可以作为padding的平替使用
- ✅ **Word居中修复**：为所有设置了max-width: 600px的表格添加align="center"属性，解决Word渲染引擎的居中问题

### v1.0 (2024-XX-XX)
- ✅ 初始版本发布
- ✅ 基础模板结构
- ✅ CSS样式系统
- ✅ 组件库建立

---

## 📞 技术支持

如有问题或建议，请联系：
- **邮箱**：<EMAIL>
- **LinkedIn**：https://www.linkedin.com/company/get3d-ai/
- **YouTube**：https://www.youtube.com/@Get3D_Official

---

*© 2025 Get3D, Inc. | #02-02, Reliance Building, 351 Jalan Besar, Singapore 208988* 