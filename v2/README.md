# Get3D Email Templates v2

## 🚀 版本说明

### 什么是v2版本？
v2版本是基于"腾讯SES优化"设计原则重新构建的邮件模板系统，专门解决海外服务器样式丢失和腾讯SES服务兼容性问题。

### 核心改进
- ✅ **100%邮件客户端兼容**：支持所有主流邮件客户端
- ✅ **腾讯SES完美支持**：针对腾讯SES服务的专门优化
- ✅ **渐进增强设计**：支持CSS3属性，不支持的客户端优雅降级
- ✅ **组件化架构**：每个组件都是独立的表格，可被提取后独立工作
- ✅ **品牌一致性**：保持Get3D品牌形象统一
- ✅ **维护简单**：使用表格布局和内联样式，降低维护复杂度

---

## 📁 文件结构

```
v2/
├── 000_Base_Template_v2.html          # 基础模板（包含所有组件参考）
├── 000_Email_Templates_Master_List_v2.md  # 模板主列表
└── README.md                          # 说明文档
```

---

## 🎯 设计原则

### "腾讯SES优化"原则
1. **表格布局为王**：放弃div、float、flexbox、grid，一切皆`<table>`
2. **内联样式必须**：不要依赖外部或头部的样式表
3. **保持简单**：避免复杂的CSS选择器、属性和JavaScript
4. **渐进增强**：支持圆角等CSS3属性，不支持的客户端优雅降级
5. **组件间距优化**：通过外层table的垂直padding实现组件间距
6. **避免阴影渐变**：不使用`box-shadow`和CSS渐变，使用纯色背景

### 技术规范
- **文档类型**：XHTML 1.0 Transitional
- **字体**：Arial, Helvetica, sans-serif（系统字体）
- **最大宽度**：600px
- **背景色**：#f2f2f2（外层），#ffffff（内容区），#e4e4e4（Footer）
- **主色调**：#d8ff18（Get3D绿），#1a1a1a（深灰）
- **变量格式**：统一使用双括号 `{{variable}}` 格式

---

## 📧 模板结构

```
Header (Get3D Logo)
├── Main Content
│   ├── Emoji Icon + Title
│   ├── Greeting
│   ├── Description
│   ├── Code Box (验证码)
│   ├── Alert Components (提示框)
│   ├── CTA Buttons (按钮)
│   ├── Card Components (卡片)
│   ├── Badge Components (徽章)
│   ├── Divider (分割线)
│   ├── List Components (列表)
│   ├── Text Components (文本)
│   ├── Image Components (图片)
│   ├── Cloud Components (云服务组件)
│   └── Content Area (实际内容区域)
└── Footer (Contact Info)
```

---

## 🔧 使用方法

### 1. 选择模板
根据邮件类型选择对应的模板类别：
- **A类**：账户验证
- **C类**：云服务
- **D类**：经销商
- **H类**：联系
- **L类**：许可证
- **M类**：营销
- **O类**：订单
- **S类**：订阅
- **T类**：试用

### 2. 替换变量
将模板中的变量占位符替换为实际值：
- `{{userName}}` - 用户名
- `{{code}}` - 验证码
- `{{cta_link}}` - 主要操作链接
- 等等...

### 3. 测试验证
在多个邮件客户端中测试显示效果：
- Gmail（网页版和移动端）
- Outlook（2019/365）
- Apple Mail
- Yahoo Mail
- 企业邮箱系统

---

## 🎨 组件库

所有组件都已集成到Base Template中，包括：

### 基础组件
- **Emoji Icon + Title**：图标和标题组合
- **Greeting**：问候语
- **Description**：描述文本
- **CTA Button**：主要操作按钮
- **Code Box**：验证码框（带圆角）

### 状态组件
- **Alert Success**：成功提示框（绿色背景，垂直居中）
- **Alert Warning**：警告提示框（黄色背景，垂直居中）
- **Alert Error**：错误提示框（红色背景，垂直居中）
- **Alert Info**：信息提示框（蓝色背景，垂直居中）

### 信息组件
- **Card**：基础卡片（白色背景，圆角）
- **Card Success**：成功卡片（绿色左边框）
- **Card Warning**：警告卡片（橙色左边框）
- **Card Error**：错误卡片（红色左边框）
- **Card Info**：信息卡片（蓝色左边框）

### Cloud专用组件
- **Task Info**：任务信息展示（绿色边框）
- **Failure Reason**：失败原因（红色背景）
- **Deletion Info**：删除信息（红色边框）
- **Expiry Info**：过期信息（橙色边框）

### 辅助组件
- **Badge**：状态徽章（ACTIVE、PENDING、EXPIRED、PROCESSING）
- **Divider**：分割线（1px高度，16px上下间距）
- **List**：列表（使用`<br>`分隔）
- **Small Text**：小文本（14px，灰色）
- **Comment Text**：注释文本（12px，更浅灰色）
- **Highlight Text**：高亮文本（带颜色强调）

### 媒体组件
- **Image**：图片组件（带圆角，响应式）

---

## 📱 兼容性

### ✅ 支持的邮件客户端
- **Gmail** (网页版和移动端)
- **Outlook** (2019/365)
- **Apple Mail**
- **Yahoo Mail**
- **企业邮箱系统**

### 🎨 渐进增强特性
- **圆角效果**：支持的客户端显示圆角，不支持的显示直角
- **纯色背景**：使用纯色背景替代渐变
- **边框效果**：使用边框替代阴影效果
- **透明度**：使用纯色替代
- **文本变换**：移除 `text-transform: uppercase`，使用大写字母
- **字间距**：移除 `letter-spacing`，确保兼容性

### 🔍 腾讯SES兼容性验证
基于腾讯SES服务的实际支持情况：
- ✅ **完全支持**：margin, padding, background-color, color, font-size, font-weight, font-family, line-height, text-decoration, border, width, height, text-align, vertical-align, display: inline-block, border-radius
- ✅ **渐进增强**：border-radius（支持的客户端显示圆角，不支持的显示直角）
- ❌ **不支持**：box-shadow（阴影效果）、CSS渐变（linear-gradient、radial-gradient）
- ✅ **替代方案**：使用纯色背景和边框替代阴影和渐变效果

### 🎨 腾讯SES优化策略
基于腾讯SES服务的实际限制：
- ✅ **style内联样式支持**：可以使用完整的CSS样式
- ✅ **圆角效果支持**：可以使用 `border-radius` 圆角效果
- ✅ **组件间距优化**：通过外层table的垂直padding实现16px间距
- ✅ **HTML属性优先**：继续使用 `bgcolor`、`align`、`valign` 等HTML属性
- ✅ **独立表格结构**：每个组件都是独立的表格，避免嵌套问题
- ✅ **双重保险策略**：同时使用HTML属性和CSS样式确保兼容性
- ❌ **避免阴影渐变**：不使用 `box-shadow` 和CSS渐变，使用纯色背景

### 🔧 技术特性
- **表格布局**：100%兼容
- **内联样式**：最大兼容性
- **系统字体**：确保显示
- **MSO支持**：Outlook兼容
- **VML背景**：Outlook背景色支持
- **独立组件**：每个组件都是独立的表格结构

---

## 🔄 从v1迁移到v2

### 主要变化
1. **布局方式**：从div布局改为表格布局
2. **样式方式**：从外部CSS改为内联样式
3. **字体**：从Google Fonts改为系统字体
4. **兼容性**：大幅提升邮件客户端兼容性
5. **变量格式**：从`${variable}`改为`{{variable}}`
6. **腾讯SES支持**：专门针对腾讯SES服务优化

### 迁移步骤
1. 使用v2的Base Template作为基础
2. 参考Base Template中的组件示例重构组件
3. 测试所有邮件客户端
4. 逐步替换现有模板

---

## 🚀 优势对比

| 特性 | v1版本 | v2版本 |
|------|--------|--------|
| 兼容性 | 中等 | 极高 |
| 样式支持 | 部分丢失 | 渐进增强 |
| 布局稳定性 | 一般 | 极佳 |
| 维护难度 | 中等 | 简单 |
| 海外服务器 | 样式丢失 | 完美显示 |
| 腾讯SES支持 | 不兼容 | 完全兼容 |
| 变量格式 | `${variable}` | `{{variable}}` |
| 布局方式 | div布局 | 表格布局 |
| 样式方式 | 外部CSS | 内联样式 |

---

## 📞 技术支持

如有问题或建议，请联系：
- **邮箱**：<EMAIL>
- **LinkedIn**：https://www.linkedin.com/company/get3d-ai/
- **YouTube**：https://www.youtube.com/@Get3D_Official

---

## 📋 更新日志

### v2.2 (2025-01-XX) - "腾讯SES优化"邮件设计
- ✅ **腾讯SES兼容策略**：针对腾讯SES服务的优化设计
- ✅ **圆角效果支持**：可以使用 `border-radius` 圆角效果
- ✅ **style内联样式支持**：可以使用完整的CSS样式
- ✅ **组件间距优化**：通过外层table的垂直padding实现16px间距
- ✅ **避免阴影渐变限制**：不使用 `box-shadow` 和CSS渐变，使用纯色背景
- ✅ **优化间距实现**：通过 `cellpadding="16"` 和 `style="padding: 16px;"` 实现组件间距
- ✅ **保持HTML属性优先**：继续使用 `bgcolor`、`align`、`valign` 等HTML属性
- ✅ **独立表格结构**：每个组件都是独立的表格，避免嵌套问题
- ✅ **双重保险策略**：同时使用HTML属性和CSS样式确保兼容性

### v2.1 (2025-01-XX) - "堡垒级"邮件设计
- ✅ **"堡垒级"防御策略**：针对Web内容安全网关的严格过滤机制
- ✅ **"家具设计"理念**：每个组件都是独立的"家具"，可被提取后独立工作
- ✅ **Web内容安全网关过滤规则**：基于实际测试发现的过滤行为
- ✅ **HTML属性优先策略**：优先使用 `bgcolor`、`align`、`valign`、`width`、`height` 等HTML属性
- ✅ **变量格式更新**：统一使用双括号 `{{variable}}` 格式
- ✅ **Footer背景色更新**：统一使用 `#e4e4e4` 背景色
- ✅ **圆角渐进增强**：恢复所有组件的 `border-radius` 属性
- ✅ **内边距优化**：修复所有组件的内边距叠加问题
- ✅ **垂直居中对齐**：修复提示框文字的垂直居中对齐
- ✅ **组件间距统一**：所有组件之间统一16px间距
- ✅ **分割线优化**：分割线只有1px上下间距，无左右内边距
- ✅ **按钮内边距修复**：避免按钮和外层td的内边距叠加
- ✅ **独立表格结构**：每个组件都是独立的表格，避免嵌套问题
- ✅ **双重保险策略**：同时使用HTML属性和CSS样式确保兼容性
- ✅ **VML背景支持**：为Outlook提供背景色支持

### v2.0 (2025-01-XX)
- ✅ 采用"回到1999年"设计原则
- ✅ 使用表格布局替代div布局
- ✅ 所有样式内联化
- ✅ 移除外部CSS依赖
- ✅ 增强邮件客户端兼容性
- ✅ 支持渐进增强特性
- ✅ 基于Can I Email专业兼容性数据优化
- ✅ 实施渐进增强策略：border-radius、Web Fonts、悬停效果
- ✅ 针对腾讯云新加坡服务器严格审查机制优化
- ✅ `<style>` 标签放在 `<body>` 内避免被过滤
- ✅ 修复所有`<p>`、`<div>`和`<span>`标签，确保100%表格布局
- ✅ 移除字段名后的冒号，优化表格显示
- ✅ 调整表格字段和值的宽度比例为40:60
- ✅ 统一变量命名规范，移除别名
- ✅ 增加图片处理规范和无障碍访问原则
- ✅ 完善Outlook条件注释说明
- ✅ 统一验证码变量为`{{code}}`，注册邮箱为`{{register}}`
- ✅ 为所有组件添加渐进式圆角（验证码框、提示框、卡片、状态徽章、列表、Cloud组件）
- ✅ 添加图片处理规范示例（alt文本、尺寸属性、圆角、绝对路径）
- ✅ 完善MSO条件注释示例
- ✅ 修复验证码框圆角位置和任务信息组件圆角缺失问题
- ✅ **关键兼容性修复**：将所有table上的margin替换为间距表格，确保Outlook兼容性
- ✅ **列表组件修复**：将td上的margin-bottom替换为padding-bottom
- ✅ **cellpadding确认**：确认cellpadding="0"是正确的，可以作为padding的平替使用
- ✅ **所有margin修复完成**：已修复所有组件的margin问题，包括基础卡片、成功卡片、警告卡片、错误卡片、信息卡片、状态徽章、分割线、列表、小文本、注释文本、高亮文本、图片示例、任务信息、失败原因、删除信息、过期信息等所有组件
- ✅ **cellpadding确认**：确认cellpadding="0"是正确的，可以作为padding的平替使用
- ✅ **Word居中修复**：为所有设置了max-width: 600px的表格添加align="center"属性，解决Word渲染引擎的居中问题

### v1.0 (2024-XX-XX)
- ✅ 初始版本发布
- ✅ 基础模板结构
- ✅ CSS样式系统
- ✅ 组件库建立

---

*© 2025 Get3D, Inc. | Email Templates v2.0* 