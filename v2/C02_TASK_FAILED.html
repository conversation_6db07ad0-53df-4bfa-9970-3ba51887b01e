<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
    xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="x-apple-disable-message-reformatting" />
    <title>❌ Task {{taskName}} failed</title>
    <!--[if gte mso 9]>
    <xml>
      <o:OfficeDocumentSettings>
        <o:AllowPNG/>
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
    <![endif]-->
</head>

<body bgcolor="#f2f2f2"
    style="padding: 0; width: 100% !important; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; font-family: Montserrat,Arial, Helvetica, sans-serif;">

    <!-- HEADER - 独立表格 -->
    <table border="0" cellpadding="0" cellspacing="0" width="100%" align="center">
        <tr>
            <td align="center" valign="top">
                <!--[if gte mso 9]>
                <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width:600px;height:80px;">
                  <v:fill type="tile" color="#1a1a1a" />
                  <v:textbox inset="0,0,0,0">
                <![endif]-->
                <table border="0" cellpadding="0" cellspacing="0" width="600" align="center">
                    <tr>
                        <td align="center" valign="middle" bgcolor="#1a1a1a" width="600" height="80" cellpadding="20"
                            cellspacing="0"
                            style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 36px; font-weight: bold; color: #d8ff18; padding: 16px;">
                            Get3D
                        </td>
                    </tr>
                </table>
                <!--[if gte mso 9]>
                  </v:textbox>
                </v:rect>
                <![endif]-->
            </td>
        </tr>
    </table>

    <!-- MAIN CONTENT - 独立表格 -->
    <table border="0" cellpadding="0" cellspacing="0" width="100%" align="center">
        <tr>
            <td align="center" valign="top">
                <!--[if gte mso 9]>
                <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width:600px;height:400px;">
                  <v:fill type="tile" color="#ffffff" />
                  <v:textbox inset="0,0,0,0">
                <![endif]-->
                <table border="0" cellpadding="0" cellspacing="0" width="600" align="center">
                    <tr>
                        <td align="left" valign="top" bgcolor="#ffffff" width="600" cellpadding="40" cellspacing="0"
                            style="padding: 40px;">

                            <!-- Emoji Icon + Title - 独立表格 -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="padding:0 0 16px 0">
                                <tr>
                                    <td align="center" valign="top" width="100%" cellpadding="20" cellspacing="0"
                                        style="font-size: 64px; line-height: 64px; padding: 8px;">❌</td>
                                </tr>
                                <tr>
                                    <td align="center" valign="top" width="100%" cellpadding="20" cellspacing="0"
                                        style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 24px; font-weight: bold; color: #333333; padding: 8px 0;">
                                        3D Modeling Task ${taskName} ailed</td>
                                </tr>
                            </table>

                            <!-- 问候语 - 独立表格 -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="padding: 0 0 8px 0;">
                                <tr>
                                    <td align="left" valign="top" width="100%" cellpadding="16" cellspacing="0"
                                        style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 16px; line-height: 24px; color: #555555; padding: 8px;">
                                        Hi {{userName}},
                                    </td>
                                </tr>
                            </table>

                            <!-- 描述文本 - 独立表格 -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="padding: 0 0 8px 0;">
                                <tr>
                                    <td align="left" valign="top" width="100%" cellpadding="16" cellspacing="0"
                                        style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 16px; line-height: 24px; color: #555555; padding: 8px;">
                                        We encountered an issue while processing your 3D modeling task "{{taskName}}. Please
                                        review the details below and try again.
                                    </td>
                                </tr>
                            </table>

                            <!-- 主要按钮 - 独立表格 -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="padding: 0 0 8px 0;">
                                <tr>
                                    <td align="center" valign="top" width="100%" cellpadding="16" cellspacing="0"
                                        style="padding: 16px;">
                                        <table border="0" cellpadding="0" cellspacing="0" align="center">
                                            <tr>
                                                <td align="center" valign="middle" bgcolor="#d8ff18" cellpadding="0"
                                                    cellspacing="0"
                                                    style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 18px; color: #1a1a1a; font-weight: bold; border-radius: 8px;">
                                                    <a href="https://www.get3d.ai/cloud"
                                                        style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 18px; color: #1a1a1a; text-decoration: none; font-weight: bold; padding: 15px 35px; display: inline-block; border-radius: 8px;">
                                                        Retry Task
                                                    </a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>

                            <!-- 任务信息组件 - 独立表格 -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="padding: 0 0 8px 0;">
                                <tr>
                                    <td align="left" valign="top" width="100%" cellpadding="28" cellspacing="0"
                                        bgcolor="#ffffff"
                                        style="border: 2px solid #e8f5e8; border-radius: 8px; padding: 16px;">
                                        <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                            <tr>
                                                <td align="left" valign="top" width="100%" cellpadding="0"
                                                    cellspacing="0"
                                                    style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 20px; font-weight: bold; color: #1c1c1c; padding-bottom: 16px;">
                                                    {{taskName}}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" valign="top" width="100%" cellpadding="0"
                                                    cellspacing="0" style="padding-bottom: 8px;">
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%"
                                                        bgcolor="#f8f9fa" style="border-radius:8px;">
                                                        <tr>
                                                            <td align="left" valign="middle" width="40%"
                                                                cellpadding="12" cellspacing="0"
                                                                style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 14px; color: #555555; font-weight: bold; padding: 12px;">
                                                                Start Time
                                                            </td>
                                                            <td align="right" valign="middle" width="60%"
                                                                cellpadding="12" cellspacing="0"
                                                                style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 14px; color: #1c1c1c; font-weight: bold; padding: 12px;">
                                                                {{startTime}}
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" valign="top" width="100%" cellpadding="0"
                                                    cellspacing="0" style="padding-top: 8px;">
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%"
                                                        bgcolor="#f8f9fa" style="border-radius:8px;">
                                                        <tr>
                                                            <td align="left" valign="middle" width="40%"
                                                                cellpadding="12" cellspacing="0"
                                                                style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 14px; color: #555555; font-weight: bold; padding: 12px;">
                                                                Completion Time
                                                            </td>
                                                            <td align="right" valign="middle" width="60%"
                                                                cellpadding="12" cellspacing="0"
                                                                style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 14px; color: #1c1c1c; font-weight: bold; padding: 12px;">
                                                                {{completionTime}}
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" valign="top" width="100%" cellpadding="0"
                                                    cellspacing="0" style="padding-top: 8px;">
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%"
                                                        bgcolor="#f8f9fa" style="border-radius:8px;">
                                                        <tr>
                                                            <td align="left" valign="middle" width="40%"
                                                                cellpadding="12" cellspacing="0"
                                                                style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 14px; color: #555555; font-weight: bold; padding: 12px;">
                                                                Duration
                                                            </td>
                                                            <td align="right" valign="middle" width="60%"
                                                                cellpadding="12" cellspacing="0"
                                                                style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 14px; color: #1c1c1c; font-weight: bold; padding: 12px;">
                                                                {{duration}}
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" valign="top" width="100%" cellpadding="0"
                                                    cellspacing="0" style="padding-top: 8px;">
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%"
                                                        bgcolor="#f8f9fa" style="border-radius:8px;">
                                                        <tr>
                                                            <td align="left" valign="middle" width="40%"
                                                                cellpadding="12" cellspacing="0"
                                                                style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 14px; color: #555555; font-weight: bold; padding: 12px;">
                                                                Images Processed
                                                            </td>
                                                            <td align="right" valign="middle" width="60%"
                                                                cellpadding="12" cellspacing="0"
                                                                style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 14px; color: #1c1c1c; font-weight: bold; padding: 12px;">
                                                                {{imageCount}}
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" valign="top" width="100%" cellpadding="0"
                                                    cellspacing="0" style="padding-top: 8px;">
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%"
                                                        bgcolor="#f8f9fa" style="border-radius:8px;">
                                                        <tr>
                                                            <td align="left" valign="middle" width="40%"
                                                                cellpadding="12" cellspacing="0"
                                                                style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 14px; color: #555555; font-weight: bold; padding: 12px;">
                                                                File Size
                                                            </td>
                                                            <td align="right" valign="middle" width="60%"
                                                                cellpadding="12" cellspacing="0"
                                                                style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 14px; color: #1c1c1c; font-weight: bold; padding: 12px;">
                                                                {{fileSize}} GB
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <table border="0" cellpadding="0" cellspacing="0" width="100%"
                                                style="padding: 8px 0 8px 0;">
                                                <tr>
                                                    <td align="left" valign="top" width="100%" cellpadding="20"
                                                        cellspacing="0" bgcolor="#fff5f5"
                                                        style="border: 1px solid #fed7d7; border-radius: 8px; padding: 16px;">
                                                        <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                                            <tr>
                                                                <td align="left" valign="top" width="100%"
                                                                    cellpadding="0" cellspacing="0"
                                                                    style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 16px; font-weight: bold; color: #c53030; padding-bottom: 12px;">
                                                                    Failure Reason
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td align="left" valign="top" width="100%"
                                                                    cellpadding="0" cellspacing="0" bgcolor="#f7fafc"
                                                                    style="border-left: 4px solid #e53e3e; border-radius: 8px; padding: 12px;">
                                                                    <table border="0" cellpadding="0" cellspacing="0"
                                                                        width="100%">
                                                                        <tr>
                                                                            <td align="left" valign="top" width="100%"
                                                                                cellpadding="0" cellspacing="0"
                                                                                style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 15px; color: #2d3748; line-height: 1.5;">
                                                                                {{failureReason}}
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                        </table>

                                        <!-- 注释文本 - 独立表格 -->
                                        <table border="0" cellpadding="0" cellspacing="0" width="100%"
                                            style="padding: 0 0 8px 0;">
                                            <tr>
                                                <td align="left" valign="top" width="100%" cellpadding="16"
                                                    cellspacing="0"
                                                    style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 12px; color: #888888; line-height: 1.3; padding: 8px;">
                                                    This email notifies you of the task failure. If you need assistance, please contact our support team.
                                                </td>
                                            </tr>
                                        </table>
                                    </td>

                                </tr>

                            </table>

                            <!-- 失败原因组件 - 独立表格 -->
                            <!--[if gte mso 9]>
                  </v:textbox>
                </v:rect>
                <![endif]-->
                        </td>
                    </tr>
                </table>

                <!-- FOOTER - 独立表格 -->
                <table border="0" cellpadding="0" cellspacing="0" width="100%" align="center">
                    <tr>
                        <td align="center" valign="top">
                            <!--[if gte mso 9]>
                <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width:600px;height:120px;">
                  <v:fill type="tile" color="#e4e4e4" />
                  <v:textbox inset="0,0,0,0">
                <![endif]-->
                            <table border="0" cellpadding="0" cellspacing="0" width="600" align="center">
                                <tr>
                                    <td align="center" valign="top" bgcolor="#e4e4e4" width="600" cellpadding="20"
                                        cellspacing="0" style="padding: 16px;">
                                        <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                            <tr>
                                                <td align="center" valign="top" width="100%" cellpadding="8"
                                                    cellspacing="0"
                                                    style="font-family: Montserrat,Arial, Helvetica, sans-serif; font-size: 14px; color: #888888; line-height: 16px; padding: 16px;">
                                                    <a href="mailto:<EMAIL>"
                                                        style="color: #555555; font-size: 18px;"><EMAIL></a>
                                                    <br><br>
                                                    <a href="https://www.linkedin.com/company/get3d-ai/"
                                                        style="color: #888888; padding: 0 4px;">LinkedIn</a>
                                                    |
                                                    <a href="https://www.youtube.com/@Get3D_Official"
                                                        style="color: #888888; padding: 0 4px;">YouTube</a>
                                                    <br><br>
                                                    © 2025 Get3D, Inc. | #02-02, Reliance Building, 351 Jalan Besar,
                                                    Singapore 208988
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                            <!--[if gte mso 9]>
                  </v:textbox>
                </v:rect>
                <![endif]-->
                        </td>
                    </tr>
                </table>

</body>

</html>