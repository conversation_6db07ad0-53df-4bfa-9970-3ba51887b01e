<!DOCTYPE html>
<html lang="en" style="margin:0;padding:0;">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
  <title>🔐 Verification code</title>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap" rel="stylesheet">
  <style>
    /* ===== BASE STYLES ===== */
    body { 
      margin:0; 
      padding:0; 
      background:#d9d9d9; 
      font-family:'Montserrat',-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",<PERSON><PERSON>,"Source Han Sans SC","Source Han Sans CN","Source Han Sans TC","Source Han Sans JP","Source Han Sans KR","PingFang SC","PingFang TC","PingFang HK","Hiragino Sans GB","Microsoft YaHei","Microsoft JhengHei","Noto Sans CJK SC","Noto Sans CJK TC","Noto Sans CJK JP","Noto Sans CJK KR",sans-serif; 
    }
    table, td { border-collapse:collapse; }
    
    /* ===== CONTAINER ===== */
    .email-container { 
      max-width:600px; 
      margin:0 auto; 
      background:#ffffff; 
      border-radius:8px; 
      box-shadow:0 0px 20px rgba(0,0,0,0.3); 
      overflow:hidden; 
    }
    
    /* ===== HEADER ===== */
    .header { 
      background:#000000; 
      padding:40px 20px; 
      text-align:center; 
      height:36px; 
      position:relative; 
      overflow:hidden; 
    }
    .header h1 { 
      margin:0; 
      color:#d8ff18; 
      font-size:32px; 
      font-weight:700; 
      letter-spacing:1px; 
      font-style:italic; 
      position:absolute; 
      top:50%; 
      left:50%; 
      transform:translate(-50%,-50%); 
      z-index:2; 
      text-shadow:2px 2px 4px rgba(0,0,0,0.5); 
    }
    
    /* ===== CONTENT AREA ===== */
    .content { 
      padding:44px 32px; 
      text-align:left; 
    }
    .content-center { text-align:center; }
    .content-left { text-align:left; }
    
    /* ===== EMOJI ICONS ===== */
    .emoji-icon { 
      font-size:64px; 
      margin:0 auto 16px; 
      display:block; 
      text-align:center; 
      line-height:1; 
    }
    
    /* ===== TYPOGRAPHY ===== */
    .title { 
      font-size:24px; 
      color:#1c1c1c; 
      margin:0 0 44px 0; 
      text-align:center; 
      font-weight:700; 
    }
    .greeting { 
      font-size:16px; 
      line-height:1.6; 
      color:#555555; 
      margin:0 0 16px 0; 
    }
    .desc { 
      font-size:16px; 
      line-height:1.6; 
      color:#555555; 
      margin:0 0 16px 0; 
    }
    .note { 
      font-size:12px; 
      color:#888888; 
      margin:16px 0 0 0; 
      line-height:1.3; 
    }
    
    /* ===== CODE/VERIFICATION ===== */
    .code-box { 
      background:#f2f2f2; 
      border-radius:12px; 
      padding:20px; 
      text-align:center; 
      margin:30px 0; 
    }
    .code { 
      margin:0; 
      font-size:32px; 
      letter-spacing:5px; 
      color:#000000; 
      font-weight:700; 
    }
    
    /* ===== BUTTONS ===== */
    .button { 
      display:block; 
      background:#c8ff29; 
      color:#1c1c1c; 
      padding:15px 35px; 
      border-radius:8px; 
      text-decoration:none; 
      font-size:18px; 
      font-weight:bold; 
      margin:0; 
      text-align:center; 
      width:100%; 
      box-sizing:border-box; 
    }
    .button:hover { background:#b3e61a; }
    
    /* ===== ALERT BOXES ===== */
    .alert { 
      padding:16px; 
      border-radius:6px; 
      margin:16px 0; 
      font-size:14px; 
    }
    .alert-warning { 
      background:#fff3cd; 
      color:#856404; 
      border:1px solid #ffeaa7; 
    }
    .alert-with-icon { 
      display:flex; 
      align-items:center; 
      gap:12px; 
    }
    .alert-icon { 
      font-size:20px; 
      flex-shrink:0; 
    }
    
    /* ===== UTILITY CLASSES ===== */
    .text-center { text-align:center; }
    .mb-16 { margin-bottom:16px; }
    .mb-24 { margin-bottom:24px; }
    
    /* ===== FOOTER ===== */
    .footer { 
      background:#f2f2f2 !important; 
      background-color:#f2f2f2 !important; 
      padding:16px; 
      text-align:center; 
      font-size:12px; 
      color:#888888; 
    }
    .footer a { 
      color:#444444; 
      text-decoration:none; 
      margin:0 5px; 
    }
    .contact { 
      color:#2b2b2b; 
      font-size:16px; 
      margin:8px 0; 
    }
    
    /* ===== RESPONSIVE ===== */
    @media screen and (max-width:400px) {
      body { 
        padding:4px; 
        font-size:16px; 
      }
      .footer { 
        background:#f2f2f2 !important; 
      }
      .content { 
        padding:20px; 
      }
      .title { 
        font-size:20px; 
        margin-bottom:32px; 
      }
      .emoji-icon { 
        font-size:48px; 
      }
      .code { 
        font-size:24px; 
        letter-spacing:3px; 
      }
      .button { 
        padding:12px 24px; 
        font-size:16px; 
      }
    }
    
    /* ===== EMAIL CLIENT COMPATIBILITY ===== */
    table, td { mso-table-lspace:0pt; mso-table-rspace:0pt; }
    img { -ms-interpolation-mode:bicubic; }
    a[x-apple-data-detectors] { color:inherit !important; text-decoration:none !important; }
  </style>
</head>
<body>
  <div class="email-container">
    <!-- Header -->
    <div class="header">
      <h1>Get3D</h1>
    </div>
    
    <!-- Content -->
    <div class="content">
      <div class="content-center">
        <div class="emoji-icon">🔐</div>
        <h2 class="title">Verification Code</h2>
      </div>
      
      <p class="greeting">Hi there,</p>
      
      <p class="desc">Please use the verification code below to verify your identity and continue with your sensitive operation.</p>
      
      <div class="code-box">
        <div class="code">${code}</div>
      </div>
      
      <div class="alert alert-warning">
        <div class="alert-with-icon">
          <span class="alert-icon">⚠️</span>
          <span>This verification code is valid for 5 minutes. For your security, do not share this code with anyone.</span>
        </div>
      </div>
      
      <p class="note">If you didn't request this verification, please ignore this email. The verification code will expire automatically.</p>
    </div>
    
    <!-- Footer -->
    <div class="footer">
      <div class="contact"><EMAIL></div>
      <p>
        <a href="https://www.linkedin.com/company/get3d-ai/">LinkedIn</a> |
        <a href="https://www.youtube.com/@Get3D_Official">YouTube</a>
      </p>
      <p>&copy; 2025 Get3D, Inc. | #02-02, Reliance Building, 351 Jalan Besar, Singapore 208988</p>
    </div>
  </div>
</body>
</html> 