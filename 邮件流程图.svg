<svg id="mermaidChart26" width="100%" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="flowchart mermaid-svg" style="max-width: 5134.453125px; color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" viewBox="-8 -8 5134.453125 4604" role="graphics-document document" aria-roledescription="flowchart-v2"><style style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">#mermaidChart26{font-family:sans-serif;font-size:16px;fill:#333;}#mermaidChart26 .error-icon{fill:#552222;}#mermaidChart26 .error-text{fill:#552222;stroke:#552222;}#mermaidChart26 .edge-thickness-normal{stroke-width:1px;}#mermaidChart26 .edge-thickness-thick{stroke-width:3.5px;}#mermaidChart26 .edge-pattern-solid{stroke-dasharray:0;}#mermaidChart26 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaidChart26 .edge-pattern-dashed{stroke-dasharray:3;}#mermaidChart26 .edge-pattern-dotted{stroke-dasharray:2;}#mermaidChart26 .marker{fill:#333333;stroke:#333333;}#mermaidChart26 .marker.cross{stroke:#333333;}#mermaidChart26 svg{font-family:sans-serif;font-size:16px;}#mermaidChart26 p{margin:0;}#mermaidChart26 .label{font-family:sans-serif;color:#333;}#mermaidChart26 .cluster-label text{fill:#333;}#mermaidChart26 .cluster-label span{color:#333;}#mermaidChart26 .cluster-label span p{background-color:transparent;}#mermaidChart26 .label text,#mermaidChart26 span{fill:#333;color:#333;}#mermaidChart26 .node rect,#mermaidChart26 .node circle,#mermaidChart26 .node ellipse,#mermaidChart26 .node polygon,#mermaidChart26 .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#mermaidChart26 .rough-node .label text,#mermaidChart26 .node .label text,#mermaidChart26 .image-shape .label,#mermaidChart26 .icon-shape .label{text-anchor:middle;}#mermaidChart26 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaidChart26 .rough-node .label,#mermaidChart26 .node .label,#mermaidChart26 .image-shape .label,#mermaidChart26 .icon-shape .label{text-align:center;}#mermaidChart26 .node.clickable{cursor:pointer;}#mermaidChart26 .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#mermaidChart26 .arrowheadPath{fill:#333333;}#mermaidChart26 .edgePath .path{stroke:#333333;stroke-width:2.0px;}#mermaidChart26 .flowchart-link{stroke:#333333;fill:none;}#mermaidChart26 .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaidChart26 .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#mermaidChart26 .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaidChart26 .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#mermaidChart26 .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#mermaidChart26 .cluster text{fill:#333;}#mermaidChart26 .cluster span{color:#333;}#mermaidChart26 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#mermaidChart26 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#mermaidChart26 rect.text{fill:none;stroke-width:0;}#mermaidChart26 .icon-shape,#mermaidChart26 .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaidChart26 .icon-shape p,#mermaidChart26 .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#mermaidChart26 .icon-shape rect,#mermaidChart26 .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaidChart26 :root{--mermaid-alt-font-family:sans-serif;}#mermaidChart26 .emailNode&gt;*{fill:#e3f2fd!important;stroke:#1976d2!important;stroke-width:2px!important;color:#000!important;}#mermaidChart26 .emailNode span{fill:#e3f2fd!important;stroke:#1976d2!important;stroke-width:2px!important;color:#000!important;}#mermaidChart26 .emailNode tspan{fill:#000!important;}#mermaidChart26 .actorNode&gt;*{fill:#f3e5f5!important;stroke:#7b1fa2!important;stroke-width:2px!important;color:#000!important;}#mermaidChart26 .actorNode span{fill:#f3e5f5!important;stroke:#7b1fa2!important;stroke-width:2px!important;color:#000!important;}#mermaidChart26 .actorNode tspan{fill:#000!important;}#mermaidChart26 .processNode&gt;*{fill:#e8f5e8!important;stroke:#388e3c!important;stroke-width:1px!important;color:#000!important;}#mermaidChart26 .processNode span{fill:#e8f5e8!important;stroke:#388e3c!important;stroke-width:1px!important;color:#000!important;}#mermaidChart26 .processNode tspan{fill:#000!important;}#mermaidChart26 .decisionNode&gt;*{fill:#fff3e0!important;stroke:#f57c00!important;stroke-width:2px!important;color:#000!important;}#mermaidChart26 .decisionNode span{fill:#fff3e0!important;stroke:#f57c00!important;stroke-width:2px!important;color:#000!important;}#mermaidChart26 .decisionNode tspan{fill:#000!important;}#mermaidChart26 .reuseNode&gt;*{fill:#ffebee!important;stroke:#d32f2f!important;stroke-width:2px!important;color:#000!important;}#mermaidChart26 .reuseNode span{fill:#ffebee!important;stroke:#d32f2f!important;stroke-width:2px!important;color:#000!important;}#mermaidChart26 .reuseNode tspan{fill:#000!important;}</style><g style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><marker id="mermaidChart26_flowchart-v2-pointEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath" style="stroke-width: 1px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></path></marker><marker id="mermaidChart26_flowchart-v2-pointStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath" style="stroke-width: 1px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></path></marker><marker id="mermaidChart26_flowchart-v2-circleEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></circle></marker><marker id="mermaidChart26_flowchart-v2-circleStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></circle></marker><marker id="mermaidChart26_flowchart-v2-crossEnd" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></path></marker><marker id="mermaidChart26_flowchart-v2-crossStart" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></path></marker><g class="root" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="clusters" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="cluster " id="Help" data-look="classic" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 255, 222); stroke: rgb(170, 170, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="2021.8671875" y="3498" width="279" height="519"></rect><g class="cluster-label " transform="translate(2116.640625, 3498)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="89.453125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🆘 客户服务</p></span></div></foreignObject></g></g><g class="cluster " id="Marketing" data-look="classic" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 255, 222); stroke: rgb(170, 170, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="4312.671875" y="771" width="805.78125" height="382"></rect><g class="cluster-label " transform="translate(4670.8359375, 771)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="89.453125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📢 营销推广</p></span></div></foreignObject></g></g><g class="cluster " id="Dealer" data-look="classic" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 255, 222); stroke: rgb(170, 170, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="3940.171875" y="771" width="352.5" height="2008"></rect><g class="cluster-label " transform="translate(4063.6953125, 771)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="105.453125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🤝 渠道商流程</p></span></div></foreignObject></g></g><g class="cluster " id="Cloud" data-look="classic" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 255, 222); stroke: rgb(170, 170, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="2377.70703125" y="2829" width="1548.765625" height="995"></rect><g class="cluster-label " transform="translate(3086.46484375, 2829)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="131.25" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">☁️ Cloud业务流程</p></span></div></foreignObject></g></g><g class="cluster " id="Subscription" data-look="classic" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 255, 222); stroke: rgb(170, 170, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="993.28125" y="2666" width="941.51953125" height="1922"></rect><g class="cluster-label " transform="translate(1419.314453125, 2666)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="89.453125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🔄 订阅管理</p></span></div></foreignObject></g></g><g class="cluster " id="Order" data-look="classic" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 255, 222); stroke: rgb(170, 170, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="3218.5859375" y="1203" width="701.5859375" height="1228"></rect><g class="cluster-label " transform="translate(3508.65234375, 1203)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="121.453125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">💰 订单交易流程</p></span></div></foreignObject></g></g><g class="cluster " id="License" data-look="classic" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 255, 222); stroke: rgb(170, 170, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="8" y="771" width="965.28125" height="2008"></rect><g class="cluster-label " transform="translate(437.9140625, 771)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="105.453125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🎁 许可证管理</p></span></div></foreignObject></g></g><g class="cluster " id="Account" data-look="classic" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 255, 222); stroke: rgb(170, 170, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="15.8046875" y="97" width="757.9765625" height="624"></rect><g class="cluster-label " transform="translate(334.06640625, 97)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="121.453125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🔐 账户管理流程</p></span></div></foreignObject></g></g><g class="cluster " id="External" data-look="classic" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 255, 222); stroke: rgb(170, 170, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="1954.80078125" y="3243" width="402.90625" height="205"></rect><g class="cluster-label " transform="translate(2111.52734375, 3243)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="89.453125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👥 外部用户</p></span></div></foreignObject></g></g><g class="cluster " id="Platform" data-look="classic" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 255, 222); stroke: rgb(170, 170, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="793.78125" y="608" width="4212.2734375" height="113"></rect><g class="cluster-label " transform="translate(2863.19140625, 608)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="73.453125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🏢 平台方</p></span></div></foreignObject></g></g></g><g class="edgePaths" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M147.5,47L147.5,51.167C147.5,55.333,147.5,63.667,147.5,72C147.5,80.333,147.5,88.667,147.5,96.333C147.5,104,147.5,111,147.5,114.5L147.5,118" id="L_START_REG_FORM_0" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M147.5,176L147.5,180.167C147.5,184.333,147.5,192.667,147.57,200.417C147.641,208.167,147.781,215.334,147.851,218.917L147.922,222.501" id="L_REG_FORM_A01_1" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M122.318,289.5L117.207,295.583C112.096,301.667,101.874,313.833,101.414,330.023C100.953,346.213,110.254,366.426,114.904,376.533L119.555,386.64" id="L_A01_VERIFY_2" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M148,534.5L147.917,540.583C147.833,546.667,147.667,558.833,147.583,571.083C147.5,583.333,147.5,595.667,147.57,605.417C147.641,615.167,147.781,622.334,147.851,625.917L147.922,629.501" id="L_VERIFY_A03_3" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M188.759,404.259L200.482,391.216C212.205,378.173,235.651,352.086,237.98,333.307C240.309,314.528,221.521,303.056,212.127,297.32L202.733,291.584" id="L_VERIFY_A01_4" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M398.695,284.5L398.695,291.417C398.695,298.333,398.695,312.167,398.775,333.667C398.855,355.167,399.014,384.333,399.094,398.917L399.173,413.5" id="L_LOGIN_FAIL_A02_5" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M399.195,480.5L399.112,495.583C399.029,510.667,398.862,540.833,398.779,562.083C398.695,583.333,398.695,595.667,398.695,606.083C398.695,616.5,398.695,625,398.695,629.25L398.695,633.5" id="L_A02_RESET_PWD_6" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M642.086,284.5L642.086,291.417C642.086,298.333,642.086,312.167,642.166,333.667C642.245,355.167,642.405,384.333,642.484,398.917L642.564,413.5" id="L_SENSITIVE_OP_A04_7" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M642.586,480.5L642.503,495.583C642.419,510.667,642.253,540.833,642.169,562.083C642.086,583.333,642.086,595.667,642.086,606.083C642.086,616.5,642.086,625,642.086,629.25L642.086,633.5" id="L_A04_OP_SUCCESS_8" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M148,696.5L147.917,700.583C147.833,704.667,147.667,712.833,147.583,721.083C147.5,729.333,147.5,737.667,147.5,746C147.5,754.333,147.5,762.667,147.5,784.083C147.5,805.5,147.5,840,147.5,857.25L147.5,874.5" id="L_A03_AUTO_TRIAL_9" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M147.5,932.5L147.5,950.417C147.5,968.333,147.5,1004.167,168.41,1027.657C189.319,1051.147,231.139,1062.294,252.048,1067.868L272.958,1073.441" id="L_AUTO_TRIAL_L01_10" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4369.641,665.669L3780.331,674.891C3191.021,684.113,2012.401,702.556,1423.091,715.945C833.781,729.333,833.781,737.667,833.781,746C833.781,754.333,833.781,762.667,833.781,784.083C833.781,805.5,833.781,840,833.781,857.25L833.781,874.5" id="L_STAFF_MANUAL_TRIAL_11" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M833.781,932.5L833.781,950.417C833.781,968.333,833.781,1004.167,771.061,1029.683C708.341,1055.199,582.9,1070.399,520.18,1077.999L457.46,1085.598" id="L_MANUAL_TRIAL_L01_12" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M497.722,2575.5L496.542,2586.417C495.361,2597.333,493.001,2619.167,491.821,2634.25C490.641,2649.333,490.641,2657.667,490.711,2665.417C490.781,2673.167,490.922,2680.334,490.992,2683.917L491.062,2687.501" id="L_PURCHASE_SUCCESS_L02_13" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M362.254,1128.5L362.171,1132.583C362.087,1136.667,361.921,1144.833,361.837,1153.083C361.754,1161.333,361.754,1169.667,361.754,1178C361.754,1186.333,361.754,1194.667,883.491,1207.23C1405.228,1219.794,2448.702,1236.588,2970.439,1244.985L3492.176,1253.383" id="L_L01_TRIAL_USE_14" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3592.676,1282L3592.676,1286.167C3592.676,1290.333,3592.676,1298.667,3592.746,1306.417C3592.816,1314.167,3592.957,1321.334,3593.027,1324.917L3593.097,1328.501" id="L_TRIAL_USE_TRIAL_WARNING_15" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3593.176,1567.5L3593.092,1571.583C3593.009,1575.667,3592.842,1583.833,3592.829,1591.5C3592.816,1599.167,3592.957,1606.334,3593.027,1609.917L3593.097,1613.501" id="L_TRIAL_WARNING_O02_16" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3593.176,1680.5L3593.092,1684.583C3593.009,1688.667,3592.842,1696.833,3592.829,1704.5C3592.816,1712.167,3592.957,1719.334,3593.027,1722.917L3593.097,1726.501" id="L_O02_TRIAL_DECISION_17" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3558.781,1835.105L3547.038,1846.921C3535.296,1858.736,3511.812,1882.368,3500.07,1899.684C3488.328,1917,3488.328,1928,3488.328,1933.5L3488.328,1939" id="L_TRIAL_DECISION_PURCHASE_18" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3637.319,1825.357L3660.778,1838.798C3684.237,1852.238,3731.156,1879.119,3754.615,1903.226C3778.074,1927.333,3778.074,1948.667,3778.074,1968C3778.074,1987.333,3778.074,2004.667,3778.074,2022C3778.074,2039.333,3778.074,2056.667,3778.074,2074C3778.074,2091.333,3778.074,2108.667,3778.074,2131.333C3778.074,2154,3778.074,2182,3778.074,2212C3778.074,2242,3778.074,2274,3778.149,2295.583C3778.223,2317.167,3778.372,2328.334,3778.446,2333.917L3778.521,2339.5" id="L_TRIAL_DECISION_O03_19" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3488.328,1997L3488.328,2001.167C3488.328,2005.333,3488.328,2013.667,3488.328,2021.333C3488.328,2029,3488.328,2036,3488.328,2039.5L3488.328,2043" id="L_PURCHASE_PAY_PROCESS_20" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3488.328,2101L3488.328,2105.167C3488.328,2109.333,3488.328,2117.667,3488.398,2125.417C3488.469,2133.167,3488.609,2140.334,3488.679,2143.917L3488.75,2147.501" id="L_PAY_PROCESS_PAY_SUCCESS_21" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3455.154,2235.826L3439.409,2247.522C3423.664,2259.217,3392.174,2282.609,3376.503,2299.888C3360.832,2317.167,3360.981,2328.334,3361.056,2333.917L3361.13,2339.5" id="L_PAY_SUCCESS_O01_22" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3515.837,2242.491L3524.761,2253.076C3533.685,2263.661,3551.532,2284.83,3560.455,2301.665C3569.379,2318.5,3569.379,2331,3569.379,2337.25L3569.379,2343.5" id="L_PAY_SUCCESS_PAY_RETRY_23" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3361.184,2406.5L3361.1,2410.583C3361.017,2414.667,3360.85,2422.833,2884.093,2431.083C2407.336,2439.333,1453.988,2447.667,977.314,2462.083C500.641,2476.5,500.641,2497,500.641,2507.25L500.641,2517.5" id="L_O01_PURCHASE_SUCCESS_24" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M516.942,2575.5L523.533,2586.417C530.124,2597.333,543.306,2619.167,549.897,2634.25C556.488,2649.333,556.488,2657.667,728.761,2670.601C901.034,2683.536,1245.58,2701.071,1417.853,2709.839L1590.126,2718.607" id="L_PURCHASE_SUCCESS_SUB_ACTIVE_25" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M1661.65,2749.5L1660.745,2754.417C1659.84,2759.333,1658.029,2769.167,1657.124,2778.25C1656.219,2787.333,1656.219,2795.667,1656.219,2804C1656.219,2812.333,1656.219,2820.667,1656.219,2828.333C1656.219,2836,1656.219,2843,1656.219,2846.5L1656.219,2850" id="L_SUB_ACTIVE_SUB_USE_26" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M1572.026,2908L1559.034,2912.167C1546.041,2916.333,1520.056,2924.667,1507.133,2932.417C1494.211,2940.167,1494.351,2947.334,1494.422,2950.917L1494.492,2954.501" id="L_SUB_USE_SUB_WARNING_27" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M1494.57,3193.5L1494.487,3197.583C1494.404,3201.667,1494.237,3209.833,1494.154,3218.083C1494.07,3226.333,1494.07,3234.667,1494.149,3250.083C1494.228,3265.5,1494.385,3288,1494.464,3299.25L1494.542,3310.5" id="L_SUB_WARNING_S01_28" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M1494.57,3377.5L1494.487,3389.25C1494.404,3401,1494.237,3424.5,1494.154,3440.417C1494.07,3456.333,1494.07,3464.667,1494.07,3473C1494.07,3481.333,1494.07,3489.667,1494.141,3497.417C1494.211,3505.167,1494.351,3512.334,1494.422,3515.917L1494.492,3519.501" id="L_S01_SUB_DECISION_29" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M1447.106,3615.036L1416.7,3629.03C1386.295,3643.024,1325.483,3671.012,1295.078,3691.256C1264.672,3711.5,1264.672,3724,1264.672,3730.25L1264.672,3736.5" id="L_SUB_DECISION_AUTO_RENEW_30" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M1488.494,3656.424L1487.723,3663.52C1486.952,3670.616,1485.409,3684.808,1484.713,3697.487C1484.016,3710.167,1484.165,3721.334,1484.239,3726.917L1484.314,3732.5" id="L_SUB_DECISION_S03_31" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M1542.28,3614.79L1573.11,3628.825C1603.94,3642.86,1665.599,3670.93,1696.503,3690.548C1727.407,3710.167,1727.556,3721.334,1727.63,3726.917L1727.704,3732.5" id="L_SUB_DECISION_S02_32" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M1264.672,3794.5L1264.672,3799.417C1264.672,3804.333,1264.672,3814.167,1264.672,3823.25C1264.672,3832.333,1264.672,3840.667,1264.742,3848.417C1264.812,3856.167,1264.953,3863.334,1265.023,3866.917L1265.093,3870.501" id="L_AUTO_RENEW_RENEW_SUCCESS_33" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M1238.386,3965.714L1231.125,3974.261C1223.865,3982.809,1209.345,3999.905,1202.084,4014.619C1194.824,4029.333,1194.824,4041.667,1194.824,4059.25C1194.824,4076.833,1194.824,4099.667,1194.824,4120.5C1194.824,4141.333,1194.824,4160.167,1194.824,4178.25C1194.824,4196.333,1194.824,4213.667,1194.824,4231C1194.824,4248.333,1194.824,4265.667,1194.824,4288.333C1194.824,4311,1194.824,4339,1194.824,4369C1194.824,4399,1194.824,4431,1240.677,4455.625C1286.53,4480.251,1378.235,4497.502,1424.087,4506.127L1469.94,4514.753" id="L_RENEW_SUCCESS_S05_34" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M1302.329,3955.343L1319.869,3965.619C1337.408,3975.895,1372.487,3996.448,1390.027,4012.89C1407.566,4029.333,1407.566,4041.667,1407.641,4053.417C1407.715,4065.167,1407.864,4076.334,1407.939,4081.917L1408.013,4087.5" id="L_RENEW_SUCCESS_O04_35" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M1408.066,4154.5L1407.983,4158.583C1407.9,4162.667,1407.733,4170.833,1407.65,4178.417C1407.566,4186,1407.566,4193,1407.566,4196.5L1407.566,4200" id="L_O04_RETRY_PAY_36" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M1407.566,4258L1407.566,4262.167C1407.566,4266.333,1407.566,4274.667,1407.637,4282.417C1407.707,4290.167,1407.847,4297.334,1407.918,4300.917L1407.988,4304.501" id="L_RETRY_PAY_FINAL_FAIL_37" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M1398.751,4417.184L1397.22,4424.82C1395.689,4432.456,1392.628,4447.728,1361.996,4463.001C1331.364,4478.273,1273.161,4493.547,1244.06,4501.184L1214.959,4508.82" id="L_FINAL_FAIL_S04_38" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M1441.048,4393.518L1455.751,4405.098C1470.453,4416.679,1499.857,4439.839,1517.284,4457.069C1534.71,4474.299,1540.158,4485.598,1542.882,4491.247L1545.607,4496.897" id="L_FINAL_FAIL_S05_39" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M1649.014,4512.013L1684.004,4503.844C1718.994,4495.675,1788.973,4479.338,1823.963,4455.169C1858.953,4431,1858.953,4399,1858.953,4369C1858.953,4339,1858.953,4311,1858.953,4288.333C1858.953,4265.667,1858.953,4248.333,1858.953,4231C1858.953,4213.667,1858.953,4196.333,1858.953,4178.25C1858.953,4160.167,1858.953,4141.333,1858.953,4120.5C1858.953,4099.667,1858.953,4076.833,1858.953,4059.25C1858.953,4041.667,1858.953,4029.333,1858.953,4009.167C1858.953,3989,1858.953,3961,1858.953,3933C1858.953,3905,1858.953,3877,1858.953,3858.833C1858.953,3840.667,1858.953,3832.333,1858.953,3818.75C1858.953,3805.167,1858.953,3786.333,1858.953,3765.5C1858.953,3744.667,1858.953,3721.833,1858.953,3692.667C1858.953,3663.5,1858.953,3628,1858.953,3594.5C1858.953,3561,1858.953,3529.5,1858.953,3509.583C1858.953,3489.667,1858.953,3481.333,1858.953,3473C1858.953,3464.667,1858.953,3456.333,1858.953,3435.083C1858.953,3413.833,1858.953,3379.667,1858.953,3345.5C1858.953,3311.333,1858.953,3277.167,1858.953,3255.917C1858.953,3234.667,1858.953,3226.333,1858.953,3198.417C1858.953,3170.5,1858.953,3123,1858.953,3075.5C1858.953,3028,1858.953,2980.5,1841.893,2952.374C1824.833,2924.248,1790.713,2915.497,1773.653,2911.121L1756.593,2906.745" id="L_S05_SUB_USE_40" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M1739.121,2742.745L1760.76,2748.788C1782.398,2754.83,1825.676,2766.915,1847.314,2777.124C1868.953,2787.333,1868.953,2795.667,1868.953,2804C1868.953,2812.333,1868.953,2820.667,2122.772,2832.899C2376.591,2845.132,2884.228,2861.264,3138.047,2869.33L3391.865,2877.396" id="L_SUB_ACTIVE_CLOUD_ACCESS_41" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3505.27,2908L3505.27,2912.167C3505.27,2916.333,3505.27,2924.667,3505.27,2947.417C3505.27,2970.167,3505.27,3007.333,3505.27,3025.917L3505.27,3044.5" id="L_CLOUD_ACCESS_TASK_SUBMIT_42" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3505.27,3102.5L3505.27,3121.75C3505.27,3141,3505.27,3179.5,3505.27,3202.917C3505.27,3226.333,3505.27,3234.667,3505.27,3250.75C3505.27,3266.833,3505.27,3290.667,3505.27,3302.583L3505.27,3314.5" id="L_TASK_SUBMIT_TASK_PROCESS_43" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3505.27,3372.5L3505.27,3385.083C3505.27,3397.667,3505.27,3422.833,3505.27,3439.583C3505.27,3456.333,3505.27,3464.667,3505.27,3473C3505.27,3481.333,3505.27,3489.667,3505.344,3499.167C3505.418,3508.667,3505.566,3519.334,3505.64,3524.667L3505.714,3530" id="L_TASK_PROCESS_TASK_RESULT_44" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3474.305,3620.536L3459.183,3633.613C3444.062,3646.69,3413.818,3672.845,3398.77,3691.506C3383.723,3710.167,3383.872,3721.334,3383.946,3726.917L3384.021,3732.5" id="L_TASK_RESULT_C01_45" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3539.761,3618.009L3558.138,3631.507C3576.515,3645.006,3613.269,3672.003,3631.721,3691.085C3650.172,3710.167,3650.321,3721.334,3650.396,3726.917L3650.47,3732.5" id="L_TASK_RESULT_C02_46" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M2052.71,3372.5L2047.202,3385.083C2041.694,3397.667,2030.679,3422.833,2110.795,3439.583C2190.91,3456.333,2362.156,3464.667,2447.779,3473C2533.402,3481.333,2533.402,3489.667,2533.402,3504.417C2533.402,3519.167,2533.402,3540.333,2533.402,3550.917L2533.402,3561.5" id="L_USER_INVITE_ORG_47" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M2533.402,3619.5L2533.402,3632.75C2533.402,3646,2533.402,3672.5,2533.477,3691.333C2533.551,3710.167,2533.7,3721.334,2533.775,3726.917L2533.849,3732.5" id="L_INVITE_ORG_C03_48" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M2057.978,3372.5L2054.926,3385.083C2051.873,3397.667,2045.769,3422.833,2173.571,3439.583C2301.374,3456.333,2563.083,3464.667,2693.938,3473C2824.793,3481.333,2824.793,3489.667,2824.793,3504.417C2824.793,3519.167,2824.793,3540.333,2824.793,3550.917L2824.793,3561.5" id="L_USER_INVITE_PROJECT_49" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M2824.793,3619.5L2824.793,3632.75C2824.793,3646,2824.793,3672.5,2824.867,3691.333C2824.942,3710.167,2825.091,3721.334,2825.165,3726.917L2825.24,3732.5" id="L_INVITE_PROJECT_C05_50" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M2067.161,3372.5L2068.389,3385.083C2069.617,3397.667,2072.072,3422.833,2246.909,3439.583C2421.746,3456.333,2768.965,3464.667,2942.574,3473C3116.184,3481.333,3116.184,3489.667,3116.184,3504.417C3116.184,3519.167,3116.184,3540.333,3116.184,3550.917L3116.184,3561.5" id="L_USER_INVITE_ORG_PROJECT_51" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3116.184,3619.5L3116.184,3632.75C3116.184,3646,3116.184,3672.5,3116.258,3691.333C3116.332,3710.167,3116.481,3721.334,3116.556,3726.917L3116.63,3732.5" id="L_INVITE_ORG_PROJECT_C04_52" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3778.574,2406.5L3778.491,2410.583C3778.408,2414.667,3778.241,2422.833,3778.158,2431.083C3778.074,2439.333,3778.074,2447.667,3778.074,2467.25C3778.074,2486.833,3778.074,2517.667,3778.074,2548.5C3778.074,2579.333,3778.074,2610.167,3778.074,2629.75C3778.074,2649.333,3778.074,2657.667,3778.074,2671.25C3778.074,2684.833,3778.074,2703.667,3778.074,2722.5C3778.074,2741.333,3778.074,2760.167,3778.074,2773.75C3778.074,2787.333,3778.074,2795.667,3778.074,2804C3778.074,2812.333,3778.074,2820.667,3778.074,2828.333C3778.074,2836,3778.074,2843,3778.074,2846.5L3778.074,2850" id="L_O03_DATA_GRACE_53" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3778.074,2908L3778.074,2912.167C3778.074,2916.333,3778.074,2924.667,3778.155,2946.75C3778.235,2968.833,3778.396,3004.667,3778.476,3022.583L3778.556,3040.5" id="L_DATA_GRACE_C06_54" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3778.574,3107.5L3778.491,3125.917C3778.408,3144.333,3778.241,3181.167,3778.158,3203.75C3778.074,3226.333,3778.074,3234.667,3778.144,3242.417C3778.215,3250.167,3778.355,3257.334,3778.426,3260.917L3778.496,3264.501" id="L_C06_GRACE_END_55" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M3778.574,3423.5L3778.491,3427.583C3778.408,3431.667,3778.241,3439.833,3778.158,3448.083C3778.074,3456.333,3778.074,3464.667,3778.074,3473C3778.074,3481.333,3778.074,3489.667,3778.152,3503.75C3778.23,3517.833,3778.387,3537.667,3778.465,3547.583L3778.543,3557.5" id="L_GRACE_END_C07_56" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4369.641,677.374L4327.438,684.645C4285.234,691.916,4200.828,706.458,4158.625,717.896C4116.422,729.333,4116.422,737.667,4116.422,746C4116.422,754.333,4116.422,762.667,4116.422,784.083C4116.422,805.5,4116.422,840,4116.422,857.25L4116.422,874.5" id="L_STAFF_DEALER_INVITE_57" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4116.422,932.5L4116.422,950.417C4116.422,968.333,4116.422,1004.167,4116.492,1025.667C4116.562,1047.167,4116.703,1054.334,4116.773,1057.917L4116.843,1061.501" id="L_DEALER_INVITE_D01_58" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4116.922,1128.5L4116.839,1132.583C4116.755,1136.667,4116.589,1144.833,4116.505,1153.083C4116.422,1161.333,4116.422,1169.667,4116.422,1178C4116.422,1186.333,4116.422,1194.667,4116.422,1202.333C4116.422,1210,4116.422,1217,4116.422,1220.5L4116.422,1224" id="L_D01_DEALER_ACCEPT_59" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4116.422,1282L4116.422,1286.167C4116.422,1290.333,4116.422,1298.667,4116.422,1321.417C4116.422,1344.167,4116.422,1381.333,4116.422,1399.917L4116.422,1418.5" id="L_DEALER_ACCEPT_DEALER_AUTH_60" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4116.422,1476.5L4116.422,1495.75C4116.422,1515,4116.422,1553.5,4116.492,1576.333C4116.562,1599.167,4116.703,1606.334,4116.773,1609.917L4116.843,1613.501" id="L_DEALER_AUTH_D02_DEALER_61" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4116.922,1680.5L4116.839,1684.583C4116.755,1688.667,4116.589,1696.833,4116.505,1711.5C4116.422,1726.167,4116.422,1747.333,4116.422,1757.917L4116.422,1768.5" id="L_D02_DEALER_DEALER_PURCHASE_62" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4116.422,1826.5L4116.422,1839.75C4116.422,1853,4116.422,1879.5,4116.422,1898.25C4116.422,1917,4116.422,1928,4116.422,1933.5L4116.422,1939" id="L_DEALER_PURCHASE_DEALER_HAS_INVENTORY_63" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4116.422,1997L4116.422,2001.167C4116.422,2005.333,4116.422,2013.667,4116.422,2021.333C4116.422,2029,4116.422,2036,4116.422,2039.5L4116.422,2043" id="L_DEALER_HAS_INVENTORY_DEALER_FIND_CLIENT_64" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4116.422,2101L4116.422,2105.167C4116.422,2109.333,4116.422,2117.667,4116.422,2128.667C4116.422,2139.667,4116.422,2153.333,4116.422,2160.167L4116.422,2167" id="L_DEALER_FIND_CLIENT_DEALER_DISTRIBUTE_65" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4116.422,2249L4116.422,2258.5C4116.422,2268,4116.422,2287,4116.422,2302.75C4116.422,2318.5,4116.422,2331,4116.422,2337.25L4116.422,2343.5" id="L_DEALER_DISTRIBUTE_SYSTEM_SEND_NOTICE_66" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4116.422,2401.5L4116.422,2406.417C4116.422,2411.333,4116.422,2421.167,4116.422,2430.25C4116.422,2439.333,4116.422,2447.667,4116.492,2455.417C4116.562,2463.167,4116.703,2470.334,4116.773,2473.917L4116.843,2477.501" id="L_SYSTEM_SEND_NOTICE_D02_ENDUSER_67" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4116.922,2616.5L4116.839,2620.583C4116.755,2624.667,4116.589,2632.833,4116.505,2641.083C4116.422,2649.333,4116.422,2657.667,4116.422,2666.083C4116.422,2674.5,4116.422,2683,4116.422,2687.25L4116.422,2691.5" id="L_D02_ENDUSER_ENDUSER_RECEIVE_68" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4444.367,691.5L4444.367,696.417C4444.367,701.333,4444.367,711.167,4444.367,720.25C4444.367,729.333,4444.367,737.667,4444.367,746C4444.367,754.333,4444.367,762.667,4444.367,784.083C4444.367,805.5,4444.367,840,4444.367,857.25L4444.367,874.5" id="L_STAFF_COUPON_ISSUE_69" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4444.367,932.5L4444.367,950.417C4444.367,968.333,4444.367,1004.167,4444.437,1025.667C4444.508,1047.167,4444.648,1054.334,4444.719,1057.917L4444.789,1061.501" id="L_COUPON_ISSUE_M01_70" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4519.094,681.321L4548.473,687.934C4577.852,694.547,4636.609,707.774,4665.988,718.553C4695.367,729.333,4695.367,737.667,4695.367,746C4695.367,754.333,4695.367,762.667,4695.367,784.083C4695.367,805.5,4695.367,840,4695.367,857.25L4695.367,874.5" id="L_STAFF_VERSION_RELEASE_71" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4695.367,932.5L4695.367,950.417C4695.367,968.333,4695.367,1004.167,4695.437,1025.667C4695.508,1047.167,4695.648,1054.334,4695.719,1057.917L4695.789,1061.501" id="L_VERSION_RELEASE_M03_72" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4519.094,672.645L4593.038,680.704C4666.982,688.763,4814.87,704.882,4888.814,717.107C4962.758,729.333,4962.758,737.667,4962.758,746C4962.758,754.333,4962.758,762.667,4962.828,770.417C4962.898,778.167,4963.039,785.334,4963.109,788.917L4963.179,792.501" id="L_STAFF_USER_INACTIVE_73" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M4963.258,1015.5L4963.174,1019.583C4963.091,1023.667,4962.924,1031.833,4962.911,1039.5C4962.898,1047.167,4963.039,1054.334,4963.109,1057.917L4963.179,1061.501" id="L_USER_INACTIVE_M02_74" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M2090.036,3372.5L2101.925,3385.083C2113.813,3397.667,2137.59,3422.833,2149.479,3439.583C2161.367,3456.333,2161.367,3464.667,2161.367,3473C2161.367,3481.333,2161.367,3489.667,2161.367,3504.417C2161.367,3519.167,2161.367,3540.333,2161.367,3550.917L2161.367,3561.5" id="L_USER_CONTACT_FORM_75" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M2161.367,3619.5L2161.367,3632.75C2161.367,3646,2161.367,3672.5,2161.442,3691.333C2161.516,3710.167,2161.665,3721.334,2161.739,3726.917L2161.814,3732.5" id="L_CONTACT_FORM_H01_76" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path><path d="M2161.867,3799.5L2161.784,3803.583C2161.701,3807.667,2161.534,3815.833,2161.451,3824.083C2161.367,3832.333,2161.367,3840.667,2161.367,3853.667C2161.367,3866.667,2161.367,3884.333,2161.367,3893.167L2161.367,3902" id="L_H01_STAFF_REPLY_77" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart26_flowchart-v2-pointEnd)"></path></g><g class="edgeLabels" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(147.5, 571)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-8, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="16" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">是</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(259.09765625, 326)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-8, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="16" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">否</p></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(3488.328125, 1906)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-16, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="32" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">购买</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(3778.07421875, 2074)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-16, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="32" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">忽略</p></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(3360.68359375, 2306)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-8, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="16" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">是</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(3569.37890625, 2306)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-8, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="16" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">否</p></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1264.671875, 3699)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-32, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="64" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">自动续费</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1483.8671875, 3699)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-32, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="64" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">手动取消</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1727.2578125, 3699)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-16, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="32" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">忽略</p></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1194.82421875, 4231)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-8, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="16" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">是</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1407.56640625, 4054)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-8, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="16" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">否</p></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1389.56640625, 4463)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-8, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="16" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">是</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1529.26171875, 4463)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-8, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="16" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">否</p></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(3383.57421875, 3699)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-16, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="32" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">成功</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(3650.0234375, 3699)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-16, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="32" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">失败</p></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g></g><g class="nodes" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="node default actorNode " id="flowchart-ADMIN-3926" transform="translate(903.5078125, 664.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-74.7265625" y="-27" width="149.453125" height="54"></rect><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-44.7265625, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="89.453125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🔧 后台系统</p></span></div></foreignObject></g></g><g class="node default actorNode " id="flowchart-STAFF-3927" transform="translate(4444.3671875, 664.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-74.7265625" y="-27" width="149.453125" height="54"></rect><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-44.7265625, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="89.453125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👨‍💼 运营人员</p></span></div></foreignObject></g></g><g class="node default actorNode " id="flowchart-USER-3928" transform="translate(2064.52734375, 3345.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-74.7265625" y="-27" width="149.453125" height="54"></rect><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-44.7265625, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="89.453125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤 终端用户</p></span></div></foreignObject></g></g><g class="node default actorNode " id="flowchart-DEALER-3929" transform="translate(2255.98046875, 3345.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-66.7265625" y="-27" width="133.453125" height="54"></rect><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-36.7265625, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="73.453125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(243, 229, 245); stroke: rgb(123, 31, 162); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🤝 渠道商</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-START-3930" transform="translate(147.5, 27.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" rx="19.5" ry="19.5" x="-68.375" y="-19.5" width="136.75" height="39"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-56, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="112" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">新用户访问平台</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-REG_FORM-3931" transform="translate(147.5, 149)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-94" y="-27" width="188" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-64, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="128" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">用户填写注册表单</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-A01-3933" transform="translate(147.5, 257.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 A01: 邮箱验证码<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default processNode " id="flowchart-VERIFY-3935" transform="translate(147.5, 448.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="85.5,0 171,-85.5 85.5,-171 0,-85.5" class="label-container" transform="translate(-85.5,85.5)" style="fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-58.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="117" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤用户验证成功</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-A03-3937" transform="translate(147.5, 664.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 A03: 注册成功<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-LOGIN_FAIL-3940" transform="translate(398.6953125, 257.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-88.5" y="-27" width="177" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-58.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="117" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤用户登录失败</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-A02-3941" transform="translate(398.6953125, 448.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 A02: 重置密码<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-RESET_PWD-3943" transform="translate(398.6953125, 664.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-104.5" y="-27" width="209" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-74.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="149" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤用户重置密码成功</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-SENSITIVE_OP-3944" transform="translate(642.0859375, 257.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-88.5" y="-27" width="177" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-58.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="117" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤用户敏感操作</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-A04-3945" transform="translate(642.0859375, 448.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 A04: 验证码<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-OP_SUCCESS-3947" transform="translate(642.0859375, 664.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-88.5" y="-27" width="177" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-58.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="117" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤用户操作完成</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-AUTO_TRIAL-3949" transform="translate(147.5, 905.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-104.5" y="-27" width="209" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-74.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="149" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🔧系统自动分配试用</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-L01-3951" transform="translate(361.75390625, 1096.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 L01: 试用分配<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-MANUAL_TRIAL-3953" transform="translate(833.78125, 905.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-104.5" y="-27" width="209" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-74.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="149" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👨‍💼运营手动分配试用</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-PURCHASE_SUCCESS-3956" transform="translate(500.640625, 2548.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-62" y="-27" width="124" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-32, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="64" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">购买成功</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-L02-3957" transform="translate(490.640625, 2722.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 L02: 产品授权<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-TRIAL_USE-3959" transform="translate(3592.67578125, 1255)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-96.5" y="-27" width="193" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-66.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="133" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤用户试用期使用</p></span></div></foreignObject></g></g><g class="node default processNode " id="flowchart-TRIAL_WARNING-3961" transform="translate(3592.67578125, 1449.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="117.5,0 235,-117.5 117.5,-235 0,-117.5" class="label-container" transform="translate(-117.5,117.5)" style="fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-90.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="181" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🔧系统检测试用即将过期</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-O02-3963" transform="translate(3592.67578125, 1648.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 191.328125,0 207.078125,-31.5 191.328125,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-103.5390625,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-80.2890625, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="160.578125" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 O02: 试用即将过期<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default processNode " id="flowchart-TRIAL_DECISION-3965" transform="translate(3592.67578125, 1799.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="69.5,0 139,-69.5 69.5,-139 0,-69.5" class="label-container" transform="translate(-69.5,69.5)" style="fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-42.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="85" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤用户决策</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-PURCHASE-3967" transform="translate(3488.328125, 1970)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-72.5" y="-27" width="145" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-42.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="85" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤用户购买</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-O03-3969" transform="translate(3778.07421875, 2374.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 O03: 试用过期<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-PAY_PROCESS-3971" transform="translate(3488.328125, 2074)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-72.5" y="-27" width="145" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-42.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="85" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🔧支付处理</p></span></div></foreignObject></g></g><g class="node default processNode " id="flowchart-PAY_SUCCESS-3973" transform="translate(3488.328125, 2210)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="59,0 118,-59 59,-118 0,-59" class="label-container" transform="translate(-59,59)" style="fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-32, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="64" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">支付成功</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-O01-3975" transform="translate(3360.68359375, 2374.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 O01: 支付成功<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-PAY_RETRY-3977" transform="translate(3569.37890625, 2374.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-62" y="-27" width="124" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-32, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="64" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">重试支付</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-SUB_ACTIVE-3981" transform="translate(1666.62109375, 2722.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-72.5" y="-27" width="145" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-42.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="85" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🔧订阅激活</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-SUB_USE-3983" transform="translate(1656.21875, 2881)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-96.5" y="-27" width="193" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-66.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="133" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤用户订阅期使用</p></span></div></foreignObject></g></g><g class="node default processNode " id="flowchart-SUB_WARNING-3985" transform="translate(1494.0703125, 3075.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="117.5,0 235,-117.5 117.5,-235 0,-117.5" class="label-container" transform="translate(-117.5,117.5)" style="fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-90.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="181" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🔧系统检测订阅即将过期</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-S01-3987" transform="translate(1494.0703125, 3345.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 189.5625,0 205.3125,-31.5 189.5625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-102.65625,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-79.40625, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="158.8125" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 S01: 订阅即将过期<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default processNode " id="flowchart-SUB_DECISION-3989" transform="translate(1494.0703125, 3592.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="69.5,0 139,-69.5 69.5,-139 0,-69.5" class="label-container" transform="translate(-69.5,69.5)" style="fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-42.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="85" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤用户决策</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-AUTO_RENEW-3991" transform="translate(1264.671875, 3767.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-72.5" y="-27" width="145" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-42.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="85" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🔧自动续费</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-S03-3993" transform="translate(1483.8671875, 3767.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 S03: 订阅取消<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-S02-3995" transform="translate(1727.2578125, 3767.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 S02: 订阅过期<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default processNode " id="flowchart-RENEW_SUCCESS-3997" transform="translate(1264.671875, 3933)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="59,0 118,-59 59,-118 0,-59" class="label-container" transform="translate(-59,59)" style="fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-32, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="64" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">续费成功</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-S05-3999" transform="translate(1561.8125, 4531.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 S05: 续费成功<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-O04-4001" transform="translate(1407.56640625, 4122.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 O04: 续费失败<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-RETRY_PAY-4003" transform="translate(1407.56640625, 4231)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-72.5" y="-27" width="145" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-42.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="85" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🔧重试支付</p></span></div></foreignObject></g></g><g class="node default processNode " id="flowchart-FINAL_FAIL-4005" transform="translate(1407.56640625, 4367)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="59,0 118,-59 59,-118 0,-59" class="label-container" transform="translate(-59,59)" style="fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-32, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="64" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">最终失败</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-S04-4007" transform="translate(1124.9765625, 4531.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 S04: 订阅暂停<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-CLOUD_ACCESS-4013" transform="translate(3505.26953125, 2881)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-109.40625" y="-27" width="218.8125" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-79.40625, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="158.8125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤用户Cloud服务访问</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-TASK_SUBMIT-4015" transform="translate(3505.26953125, 3075.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-104.5" y="-27" width="209" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-74.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="149" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤用户提交建模任务</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-TASK_PROCESS-4017" transform="translate(3505.26953125, 3345.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-88.5" y="-27" width="177" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-58.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="117" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🔧系统任务处理</p></span></div></foreignObject></g></g><g class="node default processNode " id="flowchart-TASK_RESULT-4019" transform="translate(3505.26953125, 3592.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="59,0 118,-59 59,-118 0,-59" class="label-container" transform="translate(-59,59)" style="fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-32, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="64" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">任务结果</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-C01-4021" transform="translate(3383.57421875, 3767.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 C01: 任务成功<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-C02-4023" transform="translate(3650.0234375, 3767.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 C02: 任务失败<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-INVITE_ORG-4025" transform="translate(2533.40234375, 3592.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-104.5" y="-27" width="209" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-74.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="149" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤用户邀请加入组织</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-C03-4027" transform="translate(2533.40234375, 3767.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 225.640625,0 241.390625,-31.5 225.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-120.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-97.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="194.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 C03: 组织邀请<br/>📨 发送给→ 👤被邀请用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-INVITE_PROJECT-4029" transform="translate(2824.79296875, 3592.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-104.5" y="-27" width="209" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-74.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="149" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤用户邀请加入项目</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-C05-4031" transform="translate(2824.79296875, 3767.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 225.640625,0 241.390625,-31.5 225.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-120.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-97.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="194.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 C05: 项目邀请<br/>📨 发送给→ 👤被邀请用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-INVITE_ORG_PROJECT-4033" transform="translate(3116.18359375, 3592.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-120.5" y="-27" width="241" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-90.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="181" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤用户邀请加入组织项目</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-C04-4035" transform="translate(3116.18359375, 3767.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 225.640625,0 241.390625,-31.5 225.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-120.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-97.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="194.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 C04: 组织项目邀请<br/>📨 发送给→ 👤被邀请用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-DATA_GRACE-4037" transform="translate(3778.07421875, 2881)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-113.3984375" y="-27" width="226.796875" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-83.3984375, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="166.796875" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🔧系统启动14天宽容期</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-C06-4039" transform="translate(3778.07421875, 3075.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 190.4375,0 206.1875,-31.5 190.4375,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-103.09375,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-79.84375, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="159.6875" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 C06: 数据即将清除<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default processNode " id="flowchart-GRACE_END-4041" transform="translate(3778.07421875, 3345.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="77.5,0 155,-77.5 77.5,-155 0,-77.5" class="label-container" transform="translate(-77.5,77.5)" style="fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-50.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="101" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🔧宽容期结束</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-C07-4043" transform="translate(3778.07421875, 3592.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 C07: 数据已清除<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-DEALER_INVITE-4045" transform="translate(4116.421875, 905.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-102" y="-27" width="204" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-72, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="144" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">运营邀请成为渠道商</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-D01-4047" transform="translate(4116.421875, 1096.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 193.640625,0 209.390625,-31.5 193.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-104.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-81.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="162.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 D01: 渠道商邀请<br/>📨 发送给→ 🤝渠道商</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-DEALER_ACCEPT-4049" transform="translate(4116.421875, 1255)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-96.5" y="-27" width="193" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-66.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="133" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🤝渠道商接受邀请</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-DEALER_AUTH-4051" transform="translate(4116.421875, 1449.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-80.5" y="-27" width="161" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-50.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="101" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🤝渠道商认证</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-D02_DEALER-4053" transform="translate(4116.421875, 1648.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 193.640625,0 209.390625,-31.5 193.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-104.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-81.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="162.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 D02: 渠道商授权<br/>📨 发送给→ 🤝渠道商</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-DEALER_PURCHASE-4055" transform="translate(4116.421875, 1799.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-112.5" y="-27" width="225" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-82.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="165" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🤝渠道商批量购买产品</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-DEALER_HAS_INVENTORY-4057" transform="translate(4116.421875, 1970)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-112.5" y="-27" width="225" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-82.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="165" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🤝渠道商获得分发权限</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-DEALER_FIND_CLIENT-4059" transform="translate(4116.421875, 2074)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-96.5" y="-27" width="193" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-66.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="133" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🤝渠道商寻找客户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-DEALER_DISTRIBUTE-4061" transform="translate(4116.421875, 2210)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-130" y="-39" width="260" height="78"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-100, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="200" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🤝渠道商决定分发给👤用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-SYSTEM_SEND_NOTICE-4063" transform="translate(4116.421875, 2374.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-104.5" y="-27" width="209" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-74.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="149" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">🔧后台系统发送通知</p></span></div></foreignObject></g></g><g class="node default emailNode reuseNode " id="flowchart-D02_ENDUSER-4065" transform="translate(4116.421875, 2548.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="33.75,0 248.75,0 282.5,-67.5 248.75,-135 33.75,-135 0,-67.5" class="label-container" transform="translate(-141.25,67.5)" style="fill: rgb(255, 235, 238); stroke: rgb(211, 47, 47); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(255, 235, 238); stroke: rgb(211, 47, 47); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-100, -60)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="200" height="120" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(255, 235, 238); stroke: rgb(211, 47, 47); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(255, 235, 238); stroke: rgb(211, 47, 47); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(255, 235, 238); stroke: rgb(211, 47, 47); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(255, 235, 238); stroke: rgb(211, 47, 47); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 D02: 渠道商分发通知<br/>📨 发送给→ 👤终端用户<br/>🔄 复用D02模板<br/>💬内容:ABC科技为您开通了</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-ENDUSER_RECEIVE-4067" transform="translate(4116.421875, 2722.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-104.5" y="-27" width="209" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-74.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="149" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤终端用户收到授权</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-COUPON_ISSUE-4069" transform="translate(4444.3671875, 905.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-96.5" y="-27" width="193" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-66.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="133" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👨‍💼运营发放优惠券</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-M01-4071" transform="translate(4444.3671875, 1096.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 M01: 优惠券发放<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-VERSION_RELEASE-4073" transform="translate(4695.3671875, 905.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-104.5" y="-27" width="209" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-74.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="149" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👨‍💼运营版本更新发布</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-M03-4075" transform="translate(4695.3671875, 1096.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 M03: 版本更新<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default processNode " id="flowchart-USER_INACTIVE-4077" transform="translate(4962.7578125, 905.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="109.5,0 219,-109.5 109.5,-219 0,-109.5" class="label-container" transform="translate(-109.5,109.5)" style="fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-82.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="165" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(232, 245, 232); stroke: rgb(56, 142, 60); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👨‍💼运营检测不活跃用户</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-M02-4079" transform="translate(4962.7578125, 1096.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 225.640625,0 241.390625,-31.5 225.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-120.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-97.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="194.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 M02: 用户唤醒<br/>📨 发送给→ 👤不活跃用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-CONTACT_FORM-4081" transform="translate(2161.3671875, 3592.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-104.5" y="-27" width="209" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-74.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="149" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👤用户填写联系表单</p></span></div></foreignObject></g></g><g class="node default emailNode " id="flowchart-H01-4083" transform="translate(2161.3671875, 3767.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="15.75,0 177.640625,0 193.390625,-31.5 177.640625,-63 15.75,-63 0,-31.5" class="label-container" transform="translate(-96.6953125,31.5)" style="fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.4453125, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.890625" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color: rgb(0, 0, 0); background-color: rgba(0, 0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" class="nodeLabel "><p style="background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0); fill: rgb(227, 242, 253); stroke: rgb(25, 118, 210); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">📧 H01: 联系我们<br/>📨 发送给→ 👤用户</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-STAFF_REPLY-4085" transform="translate(2161.3671875, 3933)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-88.5" y="-27" width="177" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-58.5, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="117" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">👨‍💼客服回复处理</p></span></div></foreignObject></g></g></g></g></g></svg>