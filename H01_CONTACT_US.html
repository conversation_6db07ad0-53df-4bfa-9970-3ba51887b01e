<!DOCTYPE html>
<html lang="en" style="margin:0;padding:0;">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
  <title>📧 Contact inquiry from ${fullName}</title>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap" rel="stylesheet">
  <style>
    /* ===== BASE STYLES ===== */
    body { 
      margin:0; 
      padding:0; 
      background:#d9d9d9; 
      font-family:'Montserrat',-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",<PERSON><PERSON>,"Source Han Sans SC","Source Han Sans CN","Source Han Sans TC","Source Han Sans JP","Source Han Sans KR","PingFang SC","PingFang TC","PingFang HK","Hiragino Sans GB","Microsoft YaHei","Microsoft JhengHei","Noto Sans CJK SC","Noto Sans CJK TC","Noto Sans CJK JP","Noto Sans CJK KR",sans-serif; 
    }
    table, td { border-collapse:collapse; }
    
    /* ===== CONTAINER ===== */
    .email-container { 
      max-width:600px; 
      margin:0 auto; 
      background:#ffffff; 
      border-radius:8px; 
      box-shadow:0 0px 20px rgba(0,0,0,0.3); 
      overflow:hidden; 
    }
    
    /* ===== HEADER ===== */
    .header { 
      background:#000000; 
      padding:40px 20px; 
      text-align:center; 
      height:36px; 
      position:relative; 
      overflow:hidden; 
    }
    .header h1 { 
      margin:0; 
      color:#d8ff18; 
      font-size:32px; 
      font-weight:700; 
      letter-spacing:1px; 
      font-style:italic; 
      position:absolute; 
      top:50%; 
      left:50%; 
      transform:translate(-50%,-50%); 
      z-index:2; 
      text-shadow:2px 2px 4px rgba(0,0,0,0.5); 
    }
    
    /* ===== CONTENT AREA ===== */
    .content { 
      padding:44px 32px; 
      text-align:left; 
    }
    .content-center { text-align:center; }
    .content-left { text-align:left; }
    
    /* ===== EMOJI ICONS ===== */
    .emoji-icon { 
      font-size:64px; 
      margin:0 auto 16px; 
      display:block; 
      text-align:center; 
      line-height:1; 
    }
    .emoji-icon-small { 
      font-size:32px; 
      margin:0 auto 12px; 
      display:block; 
      text-align:center; 
      line-height:1; 
    }
    .emoji-icon-tiny { 
      font-size:16px; 
      margin:0 4px 0 0; 
      display:inline-block; 
      vertical-align:middle; 
    }
    
    /* ===== TYPOGRAPHY ===== */
    .title { 
      font-size:24px; 
      color:#1c1c1c; 
      margin:0 0 44px 0; 
      text-align:center; 
      font-weight:700; 
    }
    .title-with-icon { 
      font-size:24px; 
      color:#1c1c1c; 
      margin:0 0 44px 0; 
      text-align:center; 
      font-weight:700; 
    }
    .subtitle { 
      font-size:18px; 
      font-weight:600; 
      margin:0 0 16px 0; 
      color:#333; 
    }
    .greeting { 
      font-size:16px; 
      line-height:1.6; 
      color:#555555; 
      margin:0 0 16px 0; 
    }
    .desc { 
      font-size:16px; 
      line-height:1.6; 
      color:#555555; 
      margin:0 0 16px 0; 
    }
    .note { 
      font-size:12px; 
      color:#888888; 
      margin:16px 0 0 0; 
      line-height:1.3; 
    }
    .small-text { 
      font-size:14px; 
      color:#666666; 
      line-height:1.4; 
    }
    
    /* ===== BUTTONS ===== */
    .button { 
      display:block; 
      background:#c8ff29; 
      color:#1c1c1c; 
      padding:15px 35px; 
      border-radius:8px; 
      text-decoration:none; 
      font-size:18px; 
      font-weight:bold; 
      margin:0; 
      text-align:center; 
      width:100%; 
      box-sizing:border-box; 
    }
    .button:hover { background:#b3e61a; }
    .button-primary { background:#c8ff29; color:#1c1c1c; }
    .button-success { background:#4CAF50; color:#fff; }
    .button-danger { background:#f44336; color:#fff; }
    .button-warning { background:#ff9800; color:#fff; }
    .button-info { background:#2196F3; color:#fff; }
    .button-small { 
      padding:10px 20px; 
      font-size:14px; 
    }
    .button-large { 
      padding:18px 40px; 
      font-size:20px; 
    }
    
    /* ===== ALERT BOXES ===== */
    .alert { 
      padding:16px; 
      border-radius:6px; 
      margin:16px 0; 
      font-size:14px; 
    }
    .alert-success { 
      background:#d4edda; 
      color:#155724; 
      border:1px solid #c3e6cb; 
    }
    .alert-error { 
      background:#f8d7da; 
      color:#721c24; 
      border:1px solid #f5c6cb; 
    }
    .alert-warning { 
      background:#fff3cd; 
      color:#856404; 
      border:1px solid #ffeaa7; 
    }
    .alert-info { 
      background:#d1ecf1; 
      color:#0c5460; 
      border:1px solid #bee5eb; 
    }
    .alert-with-icon { 
      display:flex; 
      align-items:center; 
      gap:12px; 
    }
    .alert-icon { 
      font-size:20px; 
      flex-shrink:0; 
    }
    
    /* ===== HIGHLIGHT TEXT ===== */
    .highlight { color:#f60505; font-weight:600; }
    .highlight-success { color:#4CAF50; font-weight:600; }
    .highlight-primary { color:#1c1c1c; font-weight:600; }
    .highlight-warning { color:#ff9800; font-weight:600; }
    .highlight-info { color:#2196F3; font-weight:600; }
    
    /* ===== CARDS ===== */
    .card { 
      border:1px solid #e0e0e0; 
      border-radius:8px; 
      padding:16px 24px; 
      margin:16px 0; 
      text-align:left; 
    }
    .card-success { 
      border-left:4px solid #4CAF50; 
      background:#f8fff8; 
    }
    .card-error { 
      border-left:4px solid #f44336; 
      background:#fff8f8; 
    }
    .card-warning { 
      border-left:4px solid #ff9800; 
      background:#fffef8; 
    }
    .card-info { 
      border-left:4px solid #2196F3; 
      background:#f8fbff; 
    }
    .card-header { 
      font-weight:600; 
      margin-bottom:16px; 
      color:#333; 
    }
    .card-content { 
      color:#555; 
      line-height:1.5; 
    }
    
    /* ===== LISTS ===== */
    .list { 
      list-style-type:none; 
      padding-left:0; 
      text-align:left; 
      list-style:none; 
      padding:0; 
    }
    .list li { 
      margin-bottom:10px; 
      padding-left:16px; 
      position:relative; 
      height:20px; 
      line-height:20px; 
    }
    .list li::before { 
      content:'•'; 
      position:absolute; 
      left:0; 
      color:#c8ff29; 
      font-size:20px; 
      line-height:20px; 
    }
    .list .list-title { 
      font-weight:bold; 
      margin-bottom:15px; 
      padding-left:0; 
    }
    .list .list-title::before { 
      content:none; 
    }
    .list-success li::before { color:#4CAF50; }
    .list-error li::before { color:#f44336; }
    .list-warning li::before { color:#ff9800; }
    .list-info li::before { color:#2196F3; }
    
    /* ===== STATUS BADGES ===== */
    .badge { 
      display:inline-block; 
      padding:4px 8px; 
      border-radius:4px; 
      font-size:12px; 
      font-weight:600; 
      text-transform:uppercase; 
    }
    .badge-success { background:#4CAF50; color:#fff; }
    .badge-error { background:#f44336; color:#fff; }
    .badge-warning { background:#ff9800; color:#fff; }
    .badge-info { background:#2196F3; color:#fff; }
    .badge-primary { background:#c8ff29; color:#1c1c1c; }
    
    /* ===== DIVIDERS ===== */
    .divider { 
      height:1px; 
      background:#e0e0e0; 
      margin:24px 0; 
    }
    .divider-dashed { 
      height:1px; 
      background:repeating-linear-gradient(to right, #e0e0e0 0, #e0e0e0 4px, transparent 4px, transparent 8px); 
      margin:24px 0; 
    }
    
    /* ===== TASK INFO STYLES ===== */
    .task-detail { 
      display:flex; 
      justify-content:space-between; 
      align-items:center; 
      margin-bottom:8px; 
      font-size:14px; 
      padding:12px 16px; 
      background:#00000008; 
      border-radius:8px; 
    }
    .task-label { 
      color:#555555; 
      font-weight:600; 
    }
    .task-value { 
      color:#1c1c1c; 
      font-weight:700; 
    }
    
    /* ===== UTILITY CLASSES ===== */
    .text-center { text-align:center; }
    .text-left { text-align:left; }
    .text-right { text-align:right; }
    .mb-0 { margin-bottom:0; }
    .mb-8 { margin-bottom:8px; }
    .mb-16 { margin-bottom:16px; }
    .mb-24 { margin-bottom:24px; }
    .mb-32 { margin-bottom:32px; }
    .mt-0 { margin-top:0; }
    .mt-8 { margin-top:8px; }
    .mt-16 { margin-top:16px; }
    .mt-24 { margin-top:24px; }
    .mt-32 { margin-top:32px; }
    .p-0 { padding:0; }
    .p-16 { padding:16px; }
    .p-24 { padding:24px; }
    
    /* ===== FOOTER ===== */
    .footer { 
      background:#f2f2f2 !important; 
      background-color:#f2f2f2 !important; 
      padding:16px; 
      text-align:center; 
      font-size:12px; 
      color:#888888; 
    }
    .footer a { 
      color:#444444; 
      text-decoration:none; 
      margin:0 5px; 
    }
    .contact { 
      color:#2b2b2b; 
      font-size:16px; 
      margin:8px 0; 
    }
    
    /* ===== RESPONSIVE ===== */
    @media screen and (max-width:400px) {
      body { 
        padding:4px; 
        font-size:16px; 
      }
      .footer { 
        background:#f2f2f2 !important; 
      }
      .content { 
        padding:20px; 
      }
      .title { 
        font-size:20px; 
        margin-bottom:32px; 
      }
      .emoji-icon { 
        font-size:48px; 
      }
      .emoji-icon-small { 
        font-size:24px; 
      }
      .code { 
        font-size:24px; 
        letter-spacing:3px; 
      }
      .code-small { 
        font-size:18px; 
        letter-spacing:2px; 
      }
      .button { 
        padding:12px 24px; 
        font-size:16px; 
      }
      .button-large { 
        padding:14px 28px; 
        font-size:18px; 
      }
      .button-small { 
        padding:8px 16px; 
        font-size:12px; 
      }
      
      /* Alert Responsive */
      .alert { 
        padding:12px; 
        margin:12px 0; 
        font-size:13px; 
      }
      .alert-icon { 
        font-size:16px; 
      }
      
      /* Card Responsive */
      .card { 
        padding:12px 16px; 
        margin:12px 0; 
      }
      .card-header { 
        font-size:15px; 
        margin-bottom:6px; 
      }
      .card-content { 
        font-size:13px; 
      }
      
      /* Task Detail Responsive */
      .task-detail { 
        font-size:14px; 
        padding:6px 8px; 
        margin-bottom:8px; 
      }
      
      /* Badge Responsive */
      .badge { 
        padding:3px 6px; 
        font-size:10px; 
      }
      
      /* Utility Classes Responsive */
      .mb-32 { margin-bottom:20px; }
      .mt-32 { margin-top:20px; }
      .p-24 { padding:16px; }
    }
  </style>
</head>
<body>
  <div class="email-container">
    <!-- Header -->
    <div class="header">
      <h1>Get3D</h1>
    </div>
    
    <!-- Content -->
    <div class="content">
      <div class="content-center">
        <div class="emoji-icon">📧</div>
        <h2 class="title">Contact inquiry from ${fullName} (${email})</h2>
      </div>
      
      <p class="greeting">Hi,</p>
      
      <p class="desc">${fullName} (${email}) from ${company} has submitted an inquiry about ${topic}.</p>
      
      <div class="text-center">
        <a href="mailto:${email}?subject=Re: ${topic} - Get3D Inquiry" class="button">Reply to Customer</a>
      </div>
      
      <div class="card card-info">
        <div class="card-header">📋 Contact Information</div>
        <div class="card-content">
          <div class="task-detail">
            <span class="task-label">Name</span>
            <span class="task-value">${fullName}</span>
          </div>
          <div class="task-detail">
            <span class="task-label">Email</span>
            <span class="task-value">${email}</span>
          </div>
          <div class="task-detail">
            <span class="task-label">Company</span>
            <span class="task-value">${company}</span>
          </div>
          <div class="task-detail">
            <span class="task-label">Topic</span>
            <span class="task-value">${topic}</span>
          </div>
          <div class="task-detail">
            <span class="task-label">Submitted</span>
            <span class="task-value">${submittedDate}</span>
          </div>
        </div>
      </div>
      <div class="card card-info">
        <div class="card-header">💬 Customer Message</div>
        <div class="card-content">
          ${message}
        </div>
      </div>
      <div class="alert alert-info">
        <div class="alert-with-icon">
          <span class="alert-icon">📋</span>
          <span>Please respond to this inquiry within 24 hours to maintain our high customer service standards.</span>
        </div>
      </div>
      

      
    </div>
    
    <!-- Footer -->
    <div class="footer">
      <div class="contact"><EMAIL></div>
      <p>
        <a href="https://www.linkedin.com/company/get3d-ai/">LinkedIn</a> |
        <a href="https://www.youtube.com/@Get3D_Official">YouTube</a>
      </p>
      <p>&copy; 2025 Get3D, Inc. | #02-02, Reliance Building, 351 Jalan Besar, Singapore 208988</p>
    </div>
  </div>
</body>
</html> 
</html> 
