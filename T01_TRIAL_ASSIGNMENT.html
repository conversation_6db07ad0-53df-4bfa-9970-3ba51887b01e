<!DOCTYPE html>
<html lang="en" style="margin:0;padding:0;">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
  <title>🎁 ${productName} trial ready</title>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap" rel="stylesheet">
  <style>
    /* ===== BASE STYLES ===== */
    body { 
      margin:0; 
      padding:0; 
      background:#d9d9d9; 
      font-family:'Montserrat',-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",<PERSON><PERSON>,"Source Han Sans SC","Source Han Sans CN","Source Han Sans TC","Source Han Sans JP","Source Han Sans KR","PingFang SC","PingFang TC","PingFang HK","Hiragino Sans GB","Microsoft YaHei","Microsoft JhengHei","Noto Sans CJK SC","Noto Sans CJK TC","Noto Sans CJK JP","Noto Sans CJK KR",sans-serif; 
    }
    table, td { border-collapse:collapse; }
    
    /* ===== CONTAINER ===== */
    .email-container { 
      max-width:600px; 
      margin:0 auto; 
      background:#ffffff; 
      border-radius:8px; 
      box-shadow:0 0px 20px rgba(0,0,0,0.3); 
      overflow:hidden; 
    }
    
    /* ===== HEADER ===== */
    .header { 
      background:linear-gradient(135deg, #1c1c1c 0%, #333333 100%); 
      padding:32px; 
      text-align:center; 
    }
    .header h1 { 
      margin:0; 
      color:#d8ff18; 
      font-size:28px; 
      font-weight:700; 
      letter-spacing:1px; 
    }
    
    /* ===== CONTENT AREA ===== */
    .content { 
      padding:44px 32px; 
      text-align:left; 
    }
    .content-center { text-align:center; }
    
    /* ===== EMOJI ICONS ===== */
    .emoji-icon { 
      font-size:64px; 
      margin:0 auto 16px; 
      display:block; 
      text-align:center; 
      line-height:1; 
    }
    
    /* ===== TYPOGRAPHY ===== */
    .title { 
      font-size:24px; 
      color:#1c1c1c; 
      margin:0 0 44px 0; 
      text-align:center; 
      font-weight:700; 
    }
    .greeting { 
      font-size:16px; 
      line-height:1.6; 
      color:#555555; 
      margin:0 0 16px 0; 
    }
    .desc { 
      font-size:16px; 
      line-height:1.6; 
      color:#555555; 
      margin:0 0 16px 0; 
    }
    .note { 
      font-size:12px; 
      color:#888888; 
      margin:16px 0 0 0; 
      line-height:1.3; 
    }
    
    /* ===== BUTTONS ===== */
    .button { 
      display:block; 
      background:#c8ff29; 
      color:#1c1c1c; 
      padding:15px 35px; 
      border-radius:8px; 
      text-decoration:none; 
      font-size:18px; 
      font-weight:bold; 
      margin:0; 
      text-align:center; 
      width:100%; 
      box-sizing:border-box; 
    }
    .button:hover { background:#b3e61a; }
    
    /* ===== CARDS ===== */
    .card { 
      border:1px solid #e8e8e8; 
      border-radius:12px; 
      padding:20px; 
      margin:20px 0; 
      text-align:left; 
      background:#fafbfc; 
      position:relative; 
    }
    .card-success { 
      border-left:4px solid #4CAF50; 
      background:linear-gradient(135deg, #f8fff8 0%, #f0f8f0 100%); 
    }
    .card-header { 
      font-weight:700; 
      margin-bottom:12px; 
      color:#2c3e50; 
      font-size:16px; 
      display:flex; 
      align-items:center; 
      gap:8px; 
    }
    .card-content { 
      color:#555; 
      line-height:1.6; 
      font-size:14px; 
    }
    
    /* ===== LICENSE INFO ===== */
    .license-info { 
      background:#ffffff; 
      border-radius:16px; 
      padding:28px; 
      margin:32px 0; 
      border:2px solid #e8f5e8; 
      box-shadow:0 4px 12px rgba(76, 175, 80, 0.1); 
      position:relative; 
    }
    .license-info::before { 
      content:''; 
      position:absolute; 
      left:0; 
      top:0; 
      bottom:0; 
      width:6px; 
      background:linear-gradient(180deg, #4CAF50 0%, #66BB6A 100%); 
      border-radius:16px 0 0 16px; 
    }
    .license-name { 
      font-size:20px; 
      font-weight:700; 
      color:#1c1c1c; 
      margin-bottom:16px; 
      padding-left:8px; 
    }
    .license-detail { 
      display:flex; 
      justify-content:space-between; 
      align-items:center; 
      margin-bottom:12px; 
      font-size:15px; 
      padding:8px 12px; 
      background:#f8f9fa; 
      border-radius:8px; 
    }
    .license-label { 
      color:#555555; 
      font-weight:600; 
    }
    .license-value { 
      color:#1c1c1c; 
      font-weight:700; 
    }
    
    /* ===== TASK INFO STYLES ===== */
    .task-detail { 
      display:flex; 
      justify-content:space-between; 
      align-items:center; 
      margin-bottom:8px; 
      font-size:14px; 
      padding:12px 16px; 
      background:#00000008; 
      border-radius:8px; 
    }
    .task-label { 
      color:#555555; 
      font-weight:600; 
    }
    .task-value { 
      color:#1c1c1c; 
      font-weight:700; 
    }
    
    /* ===== UTILITY CLASSES ===== */
    .text-center { text-align:center; }
    .mb-16 { margin-bottom:16px; }
    .mb-24 { margin-bottom:24px; }
    
    /* ===== FOOTER ===== */
    .footer { 
      background:#f2f2f2 !important; 
      background-color:#f2f2f2 !important; 
      padding:16px; 
      text-align:center; 
      font-size:12px; 
      color:#888888; 
    }
    .footer a { 
      color:#444444; 
      text-decoration:none; 
      margin:0 5px; 
    }
    .contact { 
      color:#2b2b2b; 
      font-size:16px; 
      margin:8px 0; 
    }
    
    /* ===== RESPONSIVE ===== */
    @media screen and (max-width:400px) {
      body { 
        padding:4px; 
        font-size:16px; 
      }
      .footer { 
        background:#f2f2f2 !important; 
      }
      .content { 
        padding:20px; 
      }
      .title { 
        font-size:20px; 
        margin-bottom:32px; 
      }
      .emoji-icon { 
        font-size:48px; 
      }
      .button { 
        padding:12px 24px; 
        font-size:16px; 
        width:100%; 
      }
      .license-info { 
        padding:16px; 
      }
      .license-detail { 
        flex-direction:column; 
        gap:4px; 
      }
      
      /* Task Detail Responsive */
      .task-detail { 
        font-size:14px; 
        padding:6px 8px; 
        margin-bottom:8px; 
      }
    }
    
    /* ===== EMAIL CLIENT COMPATIBILITY ===== */
    table, td { mso-table-lspace:0pt; mso-table-rspace:0pt; }
    img { -ms-interpolation-mode:bicubic; }
    a[x-apple-data-detectors] { color:inherit !important; text-decoration:none !important; }
  </style>
</head>
<body>
  <div class="email-container">
    <!-- Header -->
    <div class="header">
      <h1>Get3D</h1>
    </div>
    
    <!-- Content -->
    <div class="content">
      <div class="content-center">
        <div class="emoji-icon">🎁</div>
        <h2 class="title">Your ${productName} free trial is ready!</h2>
      </div>
      
      <p class="greeting">Hi ${userName},</p>
      
      <p class="desc">Congratulations! You've been assigned a trial license from Get3D. Start exploring our powerful 3D modeling capabilities today.</p>
      
      <div class="text-center">
        <a href="https://www.get3d.ai/personal-center/subscription" class="button">Go to My Account</a>
      </div>
      
      <div class="card card-info">
        <div class="card-header">📋 Trial License Information</div>
        <div class="card-content">
          <div class="task-detail">
            <span class="task-label">Product</span>
            <span class="task-value">${productName}</span>
          </div>
          <div class="task-detail">
            <span class="task-label">Duration</span>
            <span class="task-value">${trialDuration} days</span>
          </div>
          <div class="task-detail">
            <span class="task-label">Start Date</span>
            <span class="task-value">${startDate}</span>
          </div>
          <div class="task-detail">
            <span class="task-label">Expiry Date</span>
            <span class="task-value">${expiryDate}</span>
          </div>
        </div>
      </div>

      
      <p class="note">You can upgrade to a paid plan at any time to continue using Get3D after the trial expires.</p>
    </div>
    
    <!-- Footer -->
    <div class="footer">
      <div class="contact"><EMAIL></div>
      <p>
        <a href="https://www.linkedin.com/company/get3d-ai/">LinkedIn</a> |
        <a href="https://www.youtube.com/@Get3D_Official">YouTube</a>
      </p>
      <p>&copy; 2025 Get3D, Inc. | #02-02, Reliance Building, 351 Jalan Besar, Singapore 208988</p>
    </div>
  </div>
</body>
</html> 