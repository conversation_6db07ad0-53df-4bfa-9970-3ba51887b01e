<!DOCTYPE html>
<html lang="en" style="margin:0;padding:0;">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
  <title>⚠️ Cloud data expires soon</title>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap" rel="stylesheet">
  <style>
    /* ===== BASE STYLES ===== */
    body { 
      margin:0; 
      padding:0; 
      background:#d9d9d9; 
      font-family:'Montserrat',-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",<PERSON><PERSON>,"Source Han Sans SC","Source Han Sans CN","Source Han Sans TC","Source Han Sans JP","Source Han Sans KR","PingFang SC","PingFang TC","PingFang HK","Hiragino Sans GB","Microsoft YaHei","Microsoft JhengHei","Noto Sans CJK SC","Noto Sans CJK TC","Noto Sans CJK JP","Noto Sans CJK KR",sans-serif; 
    }
    table, td { border-collapse:collapse; }
    
    /* ===== CONTAINER ===== */
    .email-container { 
      max-width:600px; 
      margin:0 auto; 
      background:#ffffff; 
      border-radius:8px; 
      box-shadow:0 0px 20px rgba(0,0,0,0.3); 
      overflow:hidden; 
    }
    
    /* ===== HEADER ===== */
    .header { 
      background:linear-gradient(135deg, #1c1c1c 0%, #333333 100%); 
      padding:32px; 
      text-align:center; 
    }
    .header h1 { 
      margin:0; 
      color:#d8ff18; 
      font-size:28px; 
      font-weight:700; 
      letter-spacing:1px; 
    }
    
    /* ===== CONTENT AREA ===== */
    .content { 
      padding:44px 32px; 
      text-align:left; 
    }
    .content-center { text-align:center; }
    .content-left { text-align:left; }
    
    /* ===== EMOJI ICONS ===== */
    .emoji-icon { 
      font-size:64px; 
      margin:0 auto 16px; 
      display:block; 
      text-align:center; 
      line-height:1; 
    }
    
    /* ===== TYPOGRAPHY ===== */
    .title { 
      font-size:24px; 
      color:#1c1c1c; 
      margin:0 0 44px 0; 
      text-align:center; 
      font-weight:700; 
    }
    .greeting { 
      font-size:16px; 
      line-height:1.6; 
      color:#555555; 
      margin:0 0 16px 0; 
    }
    .desc { 
      font-size:16px; 
      line-height:1.6; 
      color:#555555; 
      margin:0 0 16px 0; 
    }
    .note { 
      font-size:12px; 
      color:#888888; 
      margin:16px 0 0 0; 
      line-height:1.3; 
    }
    
    /* ===== BUTTONS ===== */
    .button { 
      display:block; 
      background:#c8ff29; 
      color:#1c1c1c; 
      padding:15px 35px; 
      border-radius:8px; 
      text-decoration:none; 
      font-size:18px; 
      font-weight:bold; 
      margin:0; 
      text-align:center; 
      width:100%; 
      box-sizing:border-box; 
    }
    .button:hover { background:#b3e61a; }
    
    /* ===== ALERT BOXES ===== */
    .alert { 
      padding:16px; 
      border-radius:6px; 
      margin:16px 0; 
      font-size:14px; 
    }
    .alert-warning { 
      background:#fff3cd; 
      color:#856404; 
      border:1px solid #ffeaa7; 
    }
    .alert-with-icon { 
      display:flex; 
      align-items:center; 
      gap:12px; 
    }
    .alert-icon { 
      font-size:20px; 
      flex-shrink:0; 
    }
    
    /* ===== CARDS ===== */
    .card { 
      border:1px solid #e0e0e0; 
      border-radius:8px; 
      padding:16px 24px; 
      margin:16px 0; 
      text-align:left; 
    }
    .card-warning { 
      border-left:4px solid #ff9800; 
      background:#fffef8; 
    }
    .card-header { 
      font-weight:600; 
      margin-bottom:16px; 
      color:#333; 
    }
    .card-content { 
      color:#555; 
      line-height:1.5; 
    }
    
    /* ===== EXPIRY INFO ===== */
    .expiry-info { 
      background:#f8f9fa; 
      border-radius:12px; 
      padding:20px; 
      margin:24px 0; 
      border-left:4px solid #ff9800; 
    }
    .expiry-title { 
      font-size:18px; 
      font-weight:700; 
      color:#1c1c1c; 
      margin-bottom:12px; 
    }
    .expiry-detail { 
      display:flex; 
      justify-content:space-between; 
      margin-bottom:8px; 
      font-size:14px; 
    }
    .expiry-label { 
      color:#666666; 
      font-weight:500; 
    }
    .expiry-value { 
      color:#1c1c1c; 
      font-weight:600; 
    }
    .expiry-warning { 
      background:#fff5f5; 
      border:1px solid #fed7d7; 
      border-radius:8px; 
      padding:12px; 
      margin-top:12px; 
    }
    .expiry-warning-title { 
      font-weight:600; 
      color:#c53030; 
      margin-bottom:8px; 
    }
    .expiry-warning-text { 
      color:#2d3748; 
      font-size:14px; 
      line-height:1.5; 
    }
    
    /* ===== CARD LIST ===== */
    .card-list { 
      margin:12px 0; 
    }
    .card-list-item { 
      display:flex; 
      align-items:flex-start; 
      background:rgba(255,255,255,0.6); 
      border-radius:8px; 
      padding:12px; 
      margin-bottom:3px; 
      border:1px solid rgba(255, 152, 0, 0.2); 
    }
    .card-list-number { 
      width:24px; 
      height:24px; 
      background:rgba(255, 152, 0, 0.8); 
      border-radius:50%; 
      display:flex; 
      align-items:center; 
      justify-content:center; 
      margin-right:12px; 
      flex-shrink:0; 
      font-weight:600; 
      font-size:12px; 
      color:#ffffff; 
    }
    .card-list-content { 
      flex:1; 
    }
    .card-list-title { 
      font-weight:600; 
      font-size:14px; 
      color:#2c3e50; 
      margin-bottom:2px; 
      line-height:1.3; 
    }
    .card-list-desc { 
      font-size:13px; 
      color:#666666; 
      line-height:1.4; 
    }
    
    /* ===== TASK INFO STYLES ===== */
    .task-detail { 
      display:flex; 
      justify-content:space-between; 
      align-items:center; 
      margin-bottom:8px; 
      font-size:14px; 
      padding:12px 16px; 
      background:#00000008; 
      border-radius:8px; 
    }
    .task-label { 
      color:#555555; 
      font-weight:600; 
    }
    .task-value { 
      color:#1c1c1c; 
      font-weight:700; 
    }
    
    /* ===== UTILITY CLASSES ===== */
    .text-center { text-align:center; }
    .mb-16 { margin-bottom:16px; }
    .mb-24 { margin-bottom:24px; }
    
    /* ===== FOOTER ===== */
    .footer { 
      background:#f2f2f2 !important; 
      background-color:#f2f2f2 !important; 
      padding:16px; 
      text-align:center; 
      font-size:12px; 
      color:#888888; 
    }
    .footer a { 
      color:#444444; 
      text-decoration:none; 
      margin:0 5px; 
    }
    .contact { 
      color:#2b2b2b; 
      font-size:16px; 
      margin:8px 0; 
    }
    
    /* ===== RESPONSIVE ===== */
    @media screen and (max-width:400px) {
      body { 
        padding:4px; 
        font-size:16px; 
      }
      .footer { 
        background:#f2f2f2 !important; 
      }
      .content { 
        padding:20px; 
      }
      .title { 
        font-size:20px; 
        margin-bottom:32px; 
      }
      .emoji-icon { 
        font-size:48px; 
      }
      .button { 
        padding:12px 24px; 
        font-size:16px; 
        width:100%; 
      }
      .expiry-info { 
        padding:16px; 
      }
      .expiry-detail { 
        flex-direction:column; 
        gap:4px; 
      }
      
      /* Task Detail Responsive */
      .task-detail { 
        font-size:14px; 
        padding:6px 8px; 
        margin-bottom:8px; 
      }
    }
    
    /* ===== EMAIL CLIENT COMPATIBILITY ===== */
    table, td { mso-table-lspace:0pt; mso-table-rspace:0pt; }
    img { -ms-interpolation-mode:bicubic; }
    a[x-apple-data-detectors] { color:inherit !important; text-decoration:none !important; }
  </style>
</head>
<body>
  <div class="email-container">
    <!-- Header -->
    <div class="header">
      <h1>Get3D</h1>
    </div>
    
    <!-- Content -->
    <div class="content">
      <div class="content-center">
        <div class="emoji-icon">⚠️</div>
        <h2 class="title">Your cloud trial data expires soon</h2>
      </div>
      
      <p class="greeting">Hi ${userName},</p>
      
      <p class="desc">This is an important reminder about your Get3D Cloud trial data. Your trial period is coming to an end, and your data will be automatically deleted soon.</p>
      
      <div class="text-center">
        <a href="https://www.get3d.ai/personal-center/subscription" class="button">Upgrade Plan</a>
      </div>
      
      <div class="card card-info">
        <div class="card-header">📋 Data Expiry Information</div>
        <div class="card-content">
          <div class="task-detail">
            <span class="task-label">Expiry Date</span>
            <span class="task-value">${endDate}</span>
          </div>
          <div class="task-detail">
            <span class="task-label">Deletion Date</span>
            <span class="task-value">${deletionDate}</span>
          </div>
          <div class="task-detail">
            <span class="task-label">Grace Period</span>
            <span class="task-value">${gracePeriodDays} days</span>
          </div>
        </div>
      </div>
      
      <div class="card card-warning">
        <div class="card-header">⚠️ What will be deleted</div>
        <div class="card-content">
          All your trial data including 3D models, project files, processing results, and associated metadata will be permanently removed from our servers.
        </div>
      </div>
    
      <p class="note">This is an automated reminder about your cloud trial data expiry. If you need assistance with data backup or have questions about upgrading, please contact our support team.</p>
    </div>
    
    <!-- Footer -->
    <div class="footer">
      <div class="contact"><EMAIL></div>
      <p>
        <a href="https://www.linkedin.com/company/get3d-ai/">LinkedIn</a> |
        <a href="https://www.youtube.com/@Get3D_Official">YouTube</a>
      </p>
      <p>&copy; 2025 Get3D, Inc. | #02-02, Reliance Building, 351 Jalan Besar, Singapore 208988</p>
    </div>
  </div>
</body>
</html> 